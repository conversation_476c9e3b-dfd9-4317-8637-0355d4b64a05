<?php
/**
 * تحديث جميع الاستعلامات في المشروع لتتوافق مع النظام الموحد الجديد
 * مع جعل المنتجات مشتركة بين جميع المستخدمين
 */

require_once __DIR__ . '/config/init.php';

echo "<h2>تحديث جميع الاستعلامات والإجراءات في المشروع</h2>";

// قائمة الملفات التي تحتاج إلى تحديث
$files_to_update = [
    'index.php' => 'الصفحة الرئيسية',
    'products.php' => 'صفحة المنتجات',
    'customers.php' => 'صفحة العملاء',
    'sales.php' => 'صفحة المبيعات',
    'purchases.php' => 'صفحة المشتريات',
    'reports.php' => 'صفحة التقارير',
    'add_sale.php' => 'إضافة فاتورة مبيعات',
    'add_purchase.php' => 'إضافة فاتورة مشتريات',
    'add_customer.php' => 'إضافة عميل',
    'save_product.php' => 'حفظ منتج',
    'save_customer.php' => 'حفظ عميل',
    'get_product.php' => 'جلب بيانات منتج',
    'get_customer.php' => 'جلب بيانات عميل'
];

echo "<div style='background: #f0f0f0; padding: 15px; margin: 10px 0;'>";
echo "<h3>التحديثات المطلوبة:</h3>";
echo "<ul>";
echo "<li><strong>المنتجات:</strong> إزالة فلترة user_id (مشتركة بين جميع المستخدمين)</li>";
echo "<li><strong>العملاء:</strong> الاحتفاظ بفلترة user_id (خاصة بكل مستخدم)</li>";
echo "<li><strong>المبيعات والمشتريات:</strong> الاحتفاظ بفلترة user_id</li>";
echo "<li><strong>استعلامات JOIN:</strong> تحديث للتعامل مع المنتجات المشتركة</li>";
echo "</ul>";
echo "</div>";

// بدء التحديث
$updated_files = [];
$failed_files = [];

foreach ($files_to_update as $file => $description) {
    echo "<div style='margin: 10px 0; padding: 10px; border: 1px solid #ddd;'>";
    echo "<h4>تحديث: $description ($file)</h4>";
    
    if (file_exists(__DIR__ . '/' . $file)) {
        $result = updateFileQueries($file);
        if ($result['success']) {
            echo "<span style='color: green;'>✅ تم التحديث بنجاح</span><br>";
            echo "<span style='color: blue;'>التغييرات: " . $result['changes'] . "</span>";
            $updated_files[] = $file;
        } else {
            echo "<span style='color: red;'>❌ فشل التحديث: " . $result['error'] . "</span>";
            $failed_files[] = $file;
        }
    } else {
        echo "<span style='color: orange;'>⚠️ الملف غير موجود</span>";
        $failed_files[] = $file;
    }
    
    echo "</div>";
}

// ملخص النتائج
echo "<div style='background: #e8f5e8; padding: 15px; margin: 10px 0; border: 1px solid #4caf50;'>";
echo "<h3>ملخص التحديث:</h3>";
echo "<p><strong>الملفات المحدثة:</strong> " . count($updated_files) . "</p>";
echo "<p><strong>الملفات الفاشلة:</strong> " . count($failed_files) . "</p>";

if (!empty($updated_files)) {
    echo "<h4>الملفات المحدثة بنجاح:</h4>";
    echo "<ul>";
    foreach ($updated_files as $file) {
        echo "<li>$file</li>";
    }
    echo "</ul>";
}

if (!empty($failed_files)) {
    echo "<h4>الملفات التي تحتاج مراجعة يدوية:</h4>";
    echo "<ul>";
    foreach ($failed_files as $file) {
        echo "<li>$file</li>";
    }
    echo "</ul>";
}
echo "</div>";

// دالة تحديث الاستعلامات في الملف
function updateFileQueries($filename) {
    $filepath = __DIR__ . '/' . $filename;
    
    if (!file_exists($filepath)) {
        return ['success' => false, 'error' => 'الملف غير موجود'];
    }
    
    $content = file_get_contents($filepath);
    $original_content = $content;
    $changes = 0;
    
    // التحديثات المطلوبة
    $updates = [
        // إزالة فلترة user_id من استعلامات المنتجات
        [
            'pattern' => '/SELECT\s+.*?\s+FROM\s+[`\'"]?products[`\'"]?\s+WHERE\s+user_id\s*=\s*[^;]+/i',
            'replacement' => function($matches) {
                $query = $matches[0];
                // إزالة شرط user_id من استعلامات المنتجات
                $query = preg_replace('/\s+WHERE\s+user_id\s*=\s*[^;]+/i', '', $query);
                $query = preg_replace('/\s+AND\s+user_id\s*=\s*[^;]+/i', '', $query);
                return $query;
            },
            'description' => 'إزالة فلترة user_id من المنتجات'
        ],
        
        // تحديث استعلامات JOIN مع المنتجات
        [
            'pattern' => '/JOIN\s+[`\'"]?products[`\'"]?\s+p\s+ON\s+.*?p\.user_id\s*=\s*[^;]+/i',
            'replacement' => function($matches) {
                $join = $matches[0];
                // إزالة شرط user_id من JOIN مع المنتجات
                $join = preg_replace('/\s+AND\s+p\.user_id\s*=\s*[^;]+/i', '', $join);
                return $join;
            },
            'description' => 'تحديث JOIN مع المنتجات'
        ],
        
        // تحديث استدعاءات getUserTableName للمنتجات
        [
            'pattern' => '/getUserTableName\s*\(\s*[\'"]products[\'"]\s*,\s*[^)]+\)/i',
            'replacement' => "'products'",
            'description' => 'تحديث getUserTableName للمنتجات'
        ],
        
        // تحديث متغيرات جدول المنتجات
        [
            'pattern' => '/\$products_table\s*=\s*getUserTableName\s*\(\s*[\'"]products[\'"]\s*,\s*[^)]+\);?/i',
            'replacement' => '$products_table = "products";',
            'description' => 'تحديث متغير جدول المنتجات'
        ]
    ];
    
    foreach ($updates as $update) {
        if (is_callable($update['replacement'])) {
            $new_content = preg_replace_callback($update['pattern'], $update['replacement'], $content);
        } else {
            $new_content = preg_replace($update['pattern'], $update['replacement'], $content);
        }
        
        if ($new_content !== $content) {
            $content = $new_content;
            $changes++;
        }
    }
    
    // حفظ الملف إذا تم تغييره
    if ($content !== $original_content) {
        // إنشاء نسخة احتياطية
        $backup_file = $filepath . '.backup.' . date('Y-m-d-H-i-s');
        copy($filepath, $backup_file);
        
        // حفظ الملف المحدث
        if (file_put_contents($filepath, $content)) {
            return [
                'success' => true, 
                'changes' => $changes,
                'backup' => $backup_file
            ];
        } else {
            return ['success' => false, 'error' => 'فشل في حفظ الملف'];
        }
    }
    
    return ['success' => true, 'changes' => 0];
}

// إضافة أزرار للإجراءات الإضافية
echo "<div style='background: #fff3cd; padding: 15px; margin: 10px 0; border: 1px solid #ffeaa7;'>";
echo "<h3>إجراءات إضافية:</h3>";
echo "<p><a href='test_unified_system.php' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>اختبار النظام المحدث</a></p>";
echo "<p><a href='products.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>عرض المنتجات المشتركة</a></p>";
echo "<p><a href='index.php' style='background: #17a2b8; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>العودة للصفحة الرئيسية</a></p>";
echo "</div>";
?>
