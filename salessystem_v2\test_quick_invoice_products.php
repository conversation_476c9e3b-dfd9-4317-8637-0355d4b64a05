<?php
/**
 * اختبار المنتجات في الفاتورة السريعة
 */

require_once __DIR__ . '/config/init.php';

echo "<h2>اختبار المنتجات في الفاتورة السريعة</h2>";

$db = getDB();
if (!$db) {
    die("فشل الاتصال بقاعدة البيانات");
}

echo "<div style='background: #f0f0f0; padding: 15px; margin: 10px 0;'>";
echo "<h3>اختبار جلب المنتجات للفاتورة السريعة:</h3>";

// اختبار 1: فحص جدول المنتجات
echo "<h4>1. فحص جدول المنتجات:</h4>";
try {
    $columns = $db->query("DESCRIBE products");
    $current_columns = [];
    
    if ($columns) {
        while ($col = $columns->fetch_assoc()) {
            $current_columns[] = $col['Field'];
        }
    }
    
    $has_is_active = in_array('is_active', $current_columns);
    echo "- عمود is_active: " . ($has_is_active ? "موجود ✅" : "غير موجود ⚠️") . "<br>";
    
    $total_products = $db->query("SELECT COUNT(*) as count FROM products")->fetch_assoc()['count'];
    echo "- إجمالي المنتجات: $total_products<br>";
    
    if ($has_is_active) {
        $active_products = $db->query("SELECT COUNT(*) as count FROM products WHERE is_active = 1")->fetch_assoc()['count'];
        echo "- المنتجات النشطة: $active_products<br>";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في فحص الجدول: " . $e->getMessage() . "<br>";
}

// اختبار 2: محاكاة جلب المنتجات كما في index.php
echo "<h4>2. محاكاة جلب المنتجات للفاتورة السريعة:</h4>";
try {
    // التحقق من وجود عمود is_active
    $columns_check = $db->query("SHOW COLUMNS FROM products LIKE 'is_active'");
    $has_is_active = $columns_check && $columns_check->num_rows > 0;
    
    if ($has_is_active) {
        $products_result = $db->query("SELECT id, name, price, tax_rate, category FROM products WHERE is_active = 1 ORDER BY category, name");
        echo "✅ استخدام استعلام مع is_active = 1<br>";
    } else {
        $products_result = $db->query("SELECT id, name, price, tax_rate, category FROM products ORDER BY name");
        echo "⚠️ استخدام استعلام بدون is_active<br>";
    }
    
    if ($products_result && $products_result->num_rows > 0) {
        echo "<strong>المنتجات المتاحة للفاتورة السريعة:</strong><br>";
        echo "<table style='width: 100%; border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>ID</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>اسم المنتج</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>السعر</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>الضريبة</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>التصنيف</th>";
        echo "</tr>";
        
        $count = 0;
        while ($product = $products_result->fetch_assoc()) {
            $count++;
            echo "<tr>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . $product['id'] . "</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . htmlspecialchars($product['name']) . "</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . number_format($product['price'], 2) . " ر.س</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . number_format($product['tax_rate'], 1) . "%</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . htmlspecialchars($product['category'] ?? 'غير محدد') . "</td>";
            echo "</tr>";
            
            // عرض أول 10 منتجات فقط
            if ($count >= 10) {
                echo "<tr><td colspan='5' style='border: 1px solid #ddd; padding: 8px; text-align: center;'>... و " . ($products_result->num_rows - 10) . " منتج آخر</td></tr>";
                break;
            }
        }
        echo "</table>";
        
        echo "<span style='color: green;'>✅ تم جلب $count منتج بنجاح</span><br>";
        
    } else {
        echo "<span style='color: red;'>❌ لا توجد منتجات متاحة</span><br>";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في جلب المنتجات: " . $e->getMessage() . "<br>";
}

// اختبار 3: محاكاة JavaScript array
echo "<h4>3. محاكاة JavaScript array للمنتجات:</h4>";
try {
    echo "<div style='background: #e8f5e8; padding: 10px; margin: 10px 0;'>";
    echo "<strong>JavaScript Array:</strong><br>";
    echo "<code style='background: #f8f9fa; padding: 10px; display: block; margin: 10px 0;'>";
    echo "const quickProducts = [<br>";
    
    // إعادة تشغيل الاستعلام
    if ($has_is_active) {
        $products_result = $db->query("SELECT id, name, price, tax_rate, category FROM products WHERE is_active = 1 ORDER BY category, name");
    } else {
        $products_result = $db->query("SELECT id, name, price, tax_rate, category FROM products ORDER BY name");
    }
    
    $js_products = [];
    if ($products_result && $products_result->num_rows > 0) {
        while ($product = $products_result->fetch_assoc()) {
            $category = $product['category'] ? addslashes($product['category']) : '';
            $js_product = "{id: {$product['id']}, name: '" . addslashes($product['name']) . "', price: {$product['price']}, tax_rate: {$product['tax_rate']}, category: '$category'}";
            $js_products[] = $js_product;
            
            // عرض أول 5 منتجات فقط في الكود
            if (count($js_products) <= 5) {
                echo "&nbsp;&nbsp;" . $js_product . ",<br>";
            }
        }
        
        if (count($js_products) > 5) {
            echo "&nbsp;&nbsp;// ... و " . (count($js_products) - 5) . " منتج آخر<br>";
        }
    }
    
    echo "];<br>";
    echo "</code>";
    
    if (!empty($js_products)) {
        echo "<span style='color: green;'>✅ تم إنشاء JavaScript array بـ " . count($js_products) . " منتج</span><br>";
    } else {
        echo "<span style='color: red;'>❌ JavaScript array فارغ</span><br>";
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "❌ خطأ في إنشاء JavaScript array: " . $e->getMessage() . "<br>";
}

// اختبار 4: اختبار process_quick_invoice.php
echo "<h4>4. اختبار معالجة الفاتورة السريعة:</h4>";
try {
    // محاكاة جلب منتج واحد كما في process_quick_invoice.php
    $test_product_id = 1; // افتراض وجود منتج بـ ID = 1
    
    // التحقق من وجود المنتج
    $product_check = $db->query("SELECT COUNT(*) as count FROM products WHERE id = $test_product_id");
    $product_exists = $product_check && $product_check->fetch_assoc()['count'] > 0;
    
    if ($product_exists) {
        // محاكاة الاستعلام في process_quick_invoice.php
        if ($has_is_active) {
            $product_stmt = $db->prepare("SELECT name, category FROM products WHERE id = ? AND is_active = 1");
        } else {
            $product_stmt = $db->prepare("SELECT name, category FROM products WHERE id = ?");
        }
        
        $product_stmt->bind_param("i", $test_product_id);
        $product_stmt->execute();
        $product_result = $product_stmt->get_result();
        
        if ($product_result->num_rows > 0) {
            $product_data = $product_result->fetch_assoc();
            echo "✅ تم جلب المنتج بنجاح: " . htmlspecialchars($product_data['name']) . "<br>";
            echo "- التصنيف: " . htmlspecialchars($product_data['category'] ?? 'غير محدد') . "<br>";
        } else {
            echo "❌ فشل في جلب المنتج (قد يكون غير نشط)<br>";
        }
        $product_stmt->close();
        
    } else {
        echo "⚠️ لا يوجد منتج بـ ID = $test_product_id للاختبار<br>";
        
        // جلب أول منتج متاح
        $first_product = $db->query("SELECT id FROM products LIMIT 1");
        if ($first_product && $first_product->num_rows > 0) {
            $first_id = $first_product->fetch_assoc()['id'];
            echo "ℹ️ يمكن اختبار المنتج بـ ID = $first_id<br>";
        }
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في اختبار معالجة الفاتورة: " . $e->getMessage() . "<br>";
}

echo "</div>";

// ملخص النتائج
echo "<div style='background: #d4edda; padding: 15px; margin: 10px 0; border: 1px solid #c3e6cb;'>";
echo "<h3>✅ ملخص الاختبار:</h3>";

$issues = [];
$successes = [];

if ($total_products > 0) {
    $successes[] = "يوجد $total_products منتج في قاعدة البيانات";
} else {
    $issues[] = "لا توجد منتجات في قاعدة البيانات";
}

if ($has_is_active) {
    $successes[] = "عمود is_active موجود ويعمل بشكل صحيح";
} else {
    $issues[] = "عمود is_active غير موجود (سيعمل النظام لكن بدون فلترة النشاط)";
}

if (!empty($js_products)) {
    $successes[] = "JavaScript array للمنتجات يعمل بشكل صحيح";
} else {
    $issues[] = "JavaScript array للمنتجات فارغ";
}

echo "<h4>النجاحات:</h4>";
if (!empty($successes)) {
    echo "<ul>";
    foreach ($successes as $success) {
        echo "<li style='color: green;'>✅ $success</li>";
    }
    echo "</ul>";
}

echo "<h4>المشاكل:</h4>";
if (!empty($issues)) {
    echo "<ul>";
    foreach ($issues as $issue) {
        echo "<li style='color: red;'>❌ $issue</li>";
    }
    echo "</ul>";
} else {
    echo "<p style='color: green;'>✅ لا توجد مشاكل!</p>";
}

echo "<h4>التوصيات:</h4>";
if (empty($issues)) {
    echo "<p style='color: green;'>🎉 الفاتورة السريعة جاهزة للاستخدام!</p>";
} else {
    echo "<ul>";
    if ($total_products == 0) {
        echo "<li>أضف بعض المنتجات من صفحة المنتجات</li>";
    }
    if (!$has_is_active) {
        echo "<li>شغل أداة تحديث هيكل الجدول: <a href='update_products_table.php'>update_products_table.php</a></li>";
    }
    echo "</ul>";
}

echo "<h4>الخطوات التالية:</h4>";
echo "<p><a href='index.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>اختبار الفاتورة السريعة</a></p>";
echo "<p><a href='products.php' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>إدارة المنتجات</a></p>";
echo "</div>";
?>
