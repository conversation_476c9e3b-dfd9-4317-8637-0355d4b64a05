<?php
/**
 * صفحة التقارير
 *
 * تعرض هذه الصفحة تقارير مختلفة عن المبيعات والمشتريات والأرباح والخسائر
 * يمكن للمستخدم تصفية البيانات حسب الفترة الزمنية والعميل ونوع التقرير
 * كما يمكن طباعة التقرير أو حفظه كملف PDF
 */

// Start output buffering to prevent "headers already sent" errors
ob_start();

// يجب أن تكون هذه السطور الأولى في الملف بالضبط
require_once __DIR__ . '/config/init.php';  // هذا الملف يحتوي على تعريف isLoggedIn()
require_once __DIR__ . '/includes/functions.php';

// التأكد من ضبط ترميز UTF-8 قبل أي إخراج
header('Content-Type: text/html; charset=utf-8');

require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

$db = getCurrentUserDB();
if ($db === null || $db->connect_error) {
    $_SESSION['error'] = __('database_error') . ": " . ($db ? $db->connect_error : __('connection_error'));
    header("Location: index.php");
    exit();
}

// تنظيف أي نتائج متبقية
$db = resetDBConnection($db);

// تحديد الفترة الزمنية
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-01'); // أول يوم في الشهر الحالي
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d'); // اليوم الحالي
$report_type = isset($_GET['report_type']) ? $_GET['report_type'] : 'summary';
$customer_id = isset($_GET['customer_id']) ? intval($_GET['customer_id']) : 0;

// الحصول على أسماء الجداول مع البادئة
$username = $_SESSION['username'];
$customers_table = getUserTableName('customers', $username);
$sales_table = getUserTableName('sales', $username);
$purchases_table = getUserTableName('purchases', $username);
$products_table = getUserTableName('products', $username);
$sale_items_table = getUserTableName('sale_items', $username);

// جلب قائمة العملاء مع فلترة user_id
$customers = $db->query("SELECT id, name FROM `$customers_table` WHERE user_id = {$_SESSION['user_id']} ORDER BY name");

// بناء شروط الاستعلام مع فلترة user_id (بدون تحديد الجدول للاستخدام العام)
$date_condition_simple = " WHERE date BETWEEN '$start_date' AND '$end_date' AND user_id = {$_SESSION['user_id']} ";
$customer_condition = $customer_id > 0 ? " AND customer_id = $customer_id " : "";

// حساب إجمالي المبيعات والمشتريات مع البادئة وفلترة user_id
$sales_query = "SELECT
                COUNT(id) as count,
                SUM(total_amount) as total,
                SUM(tax_amount) as tax,
                SUM(subtotal) as subtotal,
                SUM(paid_amount) as total_paid,
                SUM(remaining_amount) as total_remaining,
                COUNT(CASE WHEN payment_status = 'paid' THEN 1 END) as paid_count,
                COUNT(CASE WHEN payment_status = 'unpaid' THEN 1 END) as unpaid_count,
                COUNT(CASE WHEN payment_status = 'partial' THEN 1 END) as partial_count
                FROM `$sales_table`
                $date_condition_simple";

// إضافة شرط العميل إذا تم تحديده
if ($customer_id > 0) {
    $sales_query .= " AND customer_id = $customer_id";
}

$sales_stats = $db->query($sales_query)->fetch_assoc();

// التحقق من وجود جدول المشتريات مع البادئة
$check_purchases_table = $db->query("SHOW TABLES LIKE '$purchases_table'");
if ($check_purchases_table && $check_purchases_table->num_rows > 0) {
    $purchases_query = "SELECT
                       COUNT(id) as count,
                       SUM(total_amount) as total,
                       SUM(tax_amount) as tax,
                       SUM(subtotal) as subtotal,
                       SUM(paid_amount) as total_paid,
                       SUM(remaining_amount) as total_remaining,
                       COUNT(CASE WHEN payment_status = 'paid' THEN 1 END) as paid_count,
                       COUNT(CASE WHEN payment_status = 'unpaid' THEN 1 END) as unpaid_count,
                       COUNT(CASE WHEN payment_status = 'partial' THEN 1 END) as partial_count
                       FROM `$purchases_table`
                       $date_condition_simple";

    // إضافة شرط العميل إذا تم تحديده
    if ($customer_id > 0) {
        // التحقق من وجود عمود customer_id
        $check_customer_column = $db->query("SHOW COLUMNS FROM `$purchases_table` LIKE 'customer_id'");
        if ($check_customer_column && $check_customer_column->num_rows > 0) {
            $purchases_query .= " AND customer_id = $customer_id";
        }
    }

    $purchases_result = $db->query($purchases_query);
    $purchases_stats = $purchases_result ? $purchases_result->fetch_assoc() : [
        'count' => 0,
        'total' => 0,
        'tax' => 0,
        'subtotal' => 0,
        'total_paid' => 0,
        'total_remaining' => 0,
        'paid_count' => 0,
        'unpaid_count' => 0,
        'partial_count' => 0
    ];
} else {
    // إذا لم يكن الجدول موجوداً، استخدم قيم افتراضية
    $purchases_stats = [
        'count' => 0,
        'total' => 0,
        'tax' => 0,
        'subtotal' => 0,
        'total_paid' => 0,
        'total_remaining' => 0,
        'paid_count' => 0,
        'unpaid_count' => 0,
        'partial_count' => 0
    ];
}

// جلب تفاصيل المبيعات مع البادئة وفلترة user_id
if ($report_type == 'sales_details') {
    // تعديل شرط التاريخ ليناسب الجدول المرتبط مع تحديد الجدول لـ user_id
    $modified_date_condition = str_replace(["WHERE date", "user_id"], ["WHERE s.date", "s.user_id"], $date_condition_simple);
    $modified_customer_condition = str_replace("customer_id", "s.customer_id", $customer_condition);

    $sales_query = "SELECT s.*, c.name as customer_name
                   FROM `$sales_table` s
                   LEFT JOIN `$customers_table` c ON s.customer_id = c.id AND c.user_id = s.user_id
                   $modified_date_condition
                   $modified_customer_condition
                   ORDER BY s.date DESC";
    $sales_result = $db->query($sales_query);
}

// جلب تفاصيل المشتريات مع البادئة وفلترة user_id
if ($report_type == 'purchases_details') {
    // التحقق من وجود جدول المشتريات
    $check_purchases_table = $db->query("SHOW TABLES LIKE '$purchases_table'");
    if ($check_purchases_table && $check_purchases_table->num_rows > 0) {
        // تعديل شرط التاريخ ليناسب الجدول المرتبط مع تحديد الجدول لـ user_id
        $modified_date_condition = str_replace(["WHERE date", "user_id"], ["WHERE p.date", "p.user_id"], $date_condition_simple);

        // التحقق من وجود عمود customer_id
        $check_customer_column = $db->query("SHOW COLUMNS FROM `$purchases_table` LIKE 'customer_id'");
        $has_customer_column = ($check_customer_column && $check_customer_column->num_rows > 0);

        if ($has_customer_column) {
            $modified_customer_condition = str_replace("customer_id", "p.customer_id", $customer_condition);
            $purchases_query = "SELECT p.*, c.name as customer_name
                               FROM `$purchases_table` p
                               LEFT JOIN `$customers_table` c ON p.customer_id = c.id AND c.user_id = p.user_id
                               $modified_date_condition
                               $modified_customer_condition
                               ORDER BY p.date DESC";
        } else {
            $purchases_query = "SELECT p.*, 'غير محدد' as customer_name
                               FROM `$purchases_table` p
                               $modified_date_condition
                               ORDER BY p.date DESC";
        }

        $purchases_result = $db->query($purchases_query);
    } else {
        $purchases_result = null;
    }
}

// جلب تفاصيل المنتجات الأكثر مبيعًا مع البادئة وفلترة user_id
if ($report_type == 'top_products') {
    // تعديل شرط التاريخ ليناسب الجدول المرتبط مع تحديد الجدول لـ user_id
    $modified_date_condition = str_replace(["WHERE date", "user_id"], ["WHERE s.date", "s.user_id"], $date_condition_simple);

    $top_products_query = "SELECT
                          si.product_id,
                          p.name as product_name,
                          SUM(si.quantity) as total_quantity,
                          SUM(si.total_price) as total_amount
                          FROM `$sale_items_table` si
                          JOIN `$sales_table` s ON si.sale_id = s.id AND si.user_id = s.user_id
                          JOIN `$products_table` p ON si.product_id = p.id AND p.user_id = si.user_id
                          $modified_date_condition
                          " . ($customer_id > 0 ? " AND s.customer_id = $customer_id " : "") . "
                          GROUP BY si.product_id
                          ORDER BY total_quantity DESC
                          LIMIT 10";
    $top_products_result = $db->query($top_products_query);
}

// جلب تفاصيل العملاء الأكثر شراءً مع البادئة وفلترة user_id
if ($report_type == 'top_customers') {
    // تعديل شرط التاريخ ليناسب الجدول المرتبط مع تحديد الجدول لـ user_id
    $modified_date_condition = str_replace(["WHERE date", "user_id"], ["WHERE s.date", "s.user_id"], $date_condition_simple);

    $top_customers_query = "SELECT
                           s.customer_id,
                           c.name as customer_name,
                           COUNT(s.id) as invoice_count,
                           SUM(s.total_amount) as total_amount
                           FROM `$sales_table` s
                           JOIN `$customers_table` c ON s.customer_id = c.id AND c.user_id = s.user_id
                           $modified_date_condition
                           GROUP BY s.customer_id
                           ORDER BY total_amount DESC
                           LIMIT 10";
    $top_customers_result = $db->query($top_customers_query);
}

// جلب كشف الحساب الشامل (مثل كشف الحساب البنكي)
if ($report_type == 'account_statement') {
    // جلب جميع المعاملات (مبيعات ومشتريات) مرتبة حسب التاريخ
    $account_transactions = [];

    // جلب المبيعات مع التحقق من النوع والبادئة وفلترة user_id
    $sales_query = "SELECT
                    s.id,
                    s.invoice_number,
                    s.date,
                    s.total_amount,
                    COALESCE(s.subtotal, s.total_amount) as subtotal,
                    COALESCE(s.tax_amount, 0) as tax_amount,
                    s.payment_status,
                    COALESCE(c.name, 'عميل غير محدد') as customer_name,
                    'sale' as transaction_type,
                    'مبيعات' as transaction_type_ar,
                    'sales' as source_table
                    FROM `$sales_table` s
                    LEFT JOIN `$customers_table` c ON s.customer_id = c.id AND c.user_id = s.user_id
                    WHERE s.date BETWEEN '$start_date' AND '$end_date' AND s.user_id = {$_SESSION['user_id']}";

    if ($customer_id > 0) {
        $sales_query .= " AND s.customer_id = $customer_id";
    }

    $sales_query .= " ORDER BY s.date ASC, s.id ASC";

    $sales_result = $db->query($sales_query);
    if ($sales_result) {
        while ($row = $sales_result->fetch_assoc()) {
            // التأكد من نوع المعاملة
            $row['transaction_type'] = 'sale';
            $row['transaction_type_ar'] = 'مبيعات';
            $account_transactions[] = $row;
        }
    }

    // جلب المشتريات (إذا كان الجدول موجود) مع البادئة وفلترة user_id
    $check_purchases_table = $db->query("SHOW TABLES LIKE '$purchases_table'");
    if ($check_purchases_table && $check_purchases_table->num_rows > 0) {
        $purchases_query = "SELECT
                           p.id,
                           p.invoice_number,
                           p.date,
                           p.total_amount,
                           COALESCE(p.subtotal, p.total_amount) as subtotal,
                           COALESCE(p.tax_amount, 0) as tax_amount,
                           p.payment_status,
                           COALESCE(c.name, p.supplier_name, 'مورد غير محدد') as customer_name,
                           'purchase' as transaction_type,
                           'مشتريات' as transaction_type_ar,
                           'purchases' as source_table
                           FROM `$purchases_table` p
                           LEFT JOIN `$customers_table` c ON p.customer_id = c.id AND c.user_id = p.user_id
                           WHERE p.date BETWEEN '$start_date' AND '$end_date' AND p.user_id = {$_SESSION['user_id']}";

        if ($customer_id > 0) {
            // التحقق من وجود عمود customer_id
            $check_customer_column = $db->query("SHOW COLUMNS FROM `$purchases_table` LIKE 'customer_id'");
            if ($check_customer_column && $check_customer_column->num_rows > 0) {
                $purchases_query .= " AND p.customer_id = $customer_id";
            }
        }

        $purchases_query .= " ORDER BY p.date ASC, p.id ASC";

        $purchases_result = $db->query($purchases_query);
        if ($purchases_result) {
            while ($row = $purchases_result->fetch_assoc()) {
                // التأكد من نوع المعاملة
                $row['transaction_type'] = 'purchase';
                $row['transaction_type_ar'] = 'مشتريات';
                $account_transactions[] = $row;
            }
        }
    }

    // ترتيب المعاملات حسب التاريخ والوقت
    usort($account_transactions, function($a, $b) {
        $date_compare = strtotime($a['date']) - strtotime($b['date']);
        if ($date_compare == 0) {
            // إذا كان التاريخ نفسه، رتب حسب المعرف
            return $a['id'] - $b['id'];
        }
        return $date_compare;
    });

    // حساب الرصيد التراكمي مع التحقق من نوع المعاملة
    $running_balance = 0;
    foreach ($account_transactions as &$transaction) {
        // التحقق المزدوج من نوع المعاملة
        $transaction_type = strtolower(trim($transaction['transaction_type']));

        if ($transaction_type === 'sale') {
            // المبيعات تزيد الرصيد (دخل)
            $running_balance += floatval($transaction['total_amount']);
        } elseif ($transaction_type === 'purchase') {
            // المشتريات تقلل الرصيد (خروج)
            $running_balance -= floatval($transaction['total_amount']);
        } else {
            // في حالة وجود نوع غير معروف، سجل خطأ
            error_log("Unknown transaction type: " . $transaction['transaction_type'] . " for transaction ID: " . $transaction['id']);
        }

        $transaction['running_balance'] = $running_balance;

        // إضافة معلومات إضافية للتحقق
        $transaction['balance_change'] = ($transaction_type === 'sale') ? '+' . number_format($transaction['total_amount'], 2) : '-' . number_format($transaction['total_amount'], 2);
    }
}

displayMessages(); // عرض أي رسائل خطأ أو نجاح
?>

<!-- CSS محسن للطباعة -->
<link rel="stylesheet" href="assets/css/print.css">
<style>
@media print {
    /* إعدادات الصفحة للتقارير */
    @page {
        size: A4 landscape;
        margin: 1cm 0.5cm;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }

    /* إخفاء العناصر غير المطلوبة */
    .no-print,
    .navbar,
    .breadcrumb,
    .btn-toolbar,
    .card-header .btn,
    .alert,
    .floating-buttons,
    .btn-group,
    .pagination,
    .form-control,
    .form-select,
    .btn:not(.print-keep),
    .search-box,
    .filter-section,
    nav,
    footer,
    .btn-info,
    .btn-secondary,
    .btn-warning,
    .btn-danger,
    .btn-outline-info,
    .btn-outline-secondary,
    .btn-sm,
    canvas,
    .chart-container,
    #profitLossChart,
    #profitLossChartContainer,
    #topProductsChartContainer,
    #topCustomersChartContainer,
    .fas.fa-eye,
    .fas.fa-print,
    .fas.fa-edit,
    .fas.fa-trash {
        display: none !important;
        visibility: hidden !important;
    }

    /* إظهار المحتوى المحدد للطباعة فقط */
    #printableContent {
        display: block !important;
        visibility: visible !important;
    }

    /* إخفاء المحتوى الأصلي عند الطباعة إذا كان هناك محتوى محدد */
    @media print {
        #reportContent > *:not(#printableContent) {
            display: none !important;
        }

        #printableContent {
            display: block !important;
        }

        /* إخفاء عمود الإجراءات في الجداول */
        .table th:last-child,
        .table td:last-child {
            display: none !important;
        }

        /* تحسين عرض الجداول */
        .table-responsive {
            overflow: visible !important;
        }
    }

    /* إعدادات أساسية */
    * {
        -webkit-print-color-adjust: exact !important;
        print-color-adjust: exact !important;
    }

    html, body {
        width: 100% !important;
        height: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
        background: white !important;
        color: #000 !important;
        font-family: 'Arial', 'Tahoma', sans-serif !important;
        font-size: 9pt !important;
        line-height: 1.3 !important;
        direction: rtl !important;
        text-align: right !important;
    }

    /* تنسيق البطاقات */
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
        margin-bottom: 10px !important;
        page-break-inside: avoid;
    }

    .card-header {
        background: #f0f0f0 !important;
        color: #000 !important;
        padding: 8px 12px !important;
        border-bottom: 1px solid #000 !important;
        font-size: 12px !important;
        font-weight: bold !important;
    }

    .card-body {
        padding: 8px !important;
    }

    /* تنسيق ملخص الرصيد */
    .row.g-0.border-bottom {
        border: 1px solid #000 !important;
        margin-bottom: 10px !important;
    }

    .row.g-0.border-bottom .col-md-2 {
        border-right: 1px solid #000 !important;
        padding: 6px 4px !important;
        text-align: center !important;
    }

    .row.g-0.border-bottom h6 {
        font-size: 9px !important;
        margin-bottom: 2px !important;
        font-weight: bold !important;
    }

    .row.g-0.border-bottom h5 {
        font-size: 10px !important;
        margin-bottom: 0 !important;
        font-weight: bold !important;
    }

    /* تنسيق الجدول */
    .table {
        font-size: 8px !important;
        margin-bottom: 0 !important;
        border-collapse: collapse !important;
        width: 100% !important;
    }

    .table th, .table td {
        border: 1px solid #000 !important;
        padding: 3px 2px !important;
        text-align: center !important;
        vertical-align: middle !important;
        line-height: 1.1 !important;
    }

    .table th {
        background: #e0e0e0 !important;
        color: #000 !important;
        font-weight: bold !important;
        font-size: 8px !important;
    }

    .table td {
        font-size: 7px !important;
    }

    /* تنسيق الألوان للطباعة */
    .text-success, .text-primary, .text-danger, .text-warning {
        color: #000 !important;
    }

    .bg-success, .bg-primary, .bg-danger, .bg-warning {
        background: #f0f0f0 !important;
        color: #000 !important;
    }

    .badge {
        border: 1px solid #000 !important;
        background: white !important;
        color: #000 !important;
        font-size: 6px !important;
        padding: 1px 3px !important;
    }

    /* تنسيق الأعمدة للطباعة */
    .table th:nth-child(1), .table td:nth-child(1) { width: 8%; }  /* التاريخ */
    .table th:nth-child(2), .table td:nth-child(2) { width: 10%; } /* رقم الفاتورة */
    .table th:nth-child(3), .table td:nth-child(3) { width: 8%; }  /* نوع المعاملة */
    .table th:nth-child(4), .table td:nth-child(4) { width: 15%; } /* العميل/المورد */
    .table th:nth-child(5), .table td:nth-child(5) { width: 8%; }  /* حالة الدفع */
    .table th:nth-child(6), .table td:nth-child(6) { width: 10%; } /* مدين */
    .table th:nth-child(7), .table td:nth-child(7) { width: 10%; } /* دائن */
    .table th:nth-child(8), .table td:nth-child(8) { width: 8%; }  /* ض.مدين */
    .table th:nth-child(9), .table td:nth-child(9) { width: 8%; }  /* ض.دائن */
    .table th:nth-child(10), .table td:nth-child(10) { width: 10%; } /* الرصيد */
    .table th:nth-child(11), .table td:nth-child(11) { width: 5%; } /* إجراءات */

    /* إخفاء عمود الإجراءات في الطباعة */
    .table th:nth-child(11), .table td:nth-child(11) {
        display: none !important;
    }

    /* تعديل عرض الأعمدة بعد إخفاء الإجراءات */
    .table th:nth-child(1), .table td:nth-child(1) { width: 9%; }  /* التاريخ */
    .table th:nth-child(2), .table td:nth-child(2) { width: 11%; } /* رقم الفاتورة */
    .table th:nth-child(3), .table td:nth-child(3) { width: 9%; }  /* نوع المعاملة */
    .table th:nth-child(4), .table td:nth-child(4) { width: 16%; } /* العميل/المورد */
    .table th:nth-child(5), .table td:nth-child(5) { width: 9%; }  /* حالة الدفع */
    .table th:nth-child(6), .table td:nth-child(6) { width: 11%; } /* مدين */
    .table th:nth-child(7), .table td:nth-child(7) { width: 11%; } /* دائن */
    .table th:nth-child(8), .table td:nth-child(8) { width: 9%; }  /* ض.مدين */
    .table th:nth-child(9), .table td:nth-child(9) { width: 9%; }  /* ض.دائن */
    .table th:nth-child(10), .table td:nth-child(10) { width: 11%; } /* الرصيد */

    /* تنسيق النصوص المحاذاة */
    .text-end {
        text-align: right !important;
    }

    .text-center {
        text-align: center !important;
    }

    /* تنسيق الأيقونات */
    .fas, .far, .fab {
        font-size: 6px !important;
    }

    /* تنسيق العناوين */
    h1, h2, h3, h4, h5, h6 {
        color: #000 !important;
        margin: 5px 0 !important;
    }

    /* تجنب كسر الصفحة داخل الجداول */
    .table-responsive {
        page-break-inside: avoid;
    }

    /* تنسيق الحاوي الرئيسي */
    .container {
        max-width: 100% !important;
        padding: 0 !important;
        margin: 0 !important;
    }

    /* إضافة معلومات الطباعة */
    .print-info {
        display: block !important;
        text-align: center;
        font-size: 8px;
        margin-top: 10px;
        border-top: 1px solid #000;
        padding-top: 5px;
    }

    /* تنسيق خاص لكشف الحساب */
    #reportContent .card-body {
        padding: 5px !important;
    }

    /* تحسين المساحات */
    .mb-4, .mb-3, .mb-2 {
        margin-bottom: 8px !important;
    }

    .py-5 {
        padding: 10px 0 !important;
    }

    /* تنسيق الجدول المتجاوب */
    .table-responsive {
        overflow: visible !important;
    }

    /* تنسيق صف الإجماليات */
    .table tfoot th {
        background: #d0d0d0 !important;
        font-weight: bold !important;
        font-size: 8px !important;
    }
}

/* تنسيق خاص لمعاينة الطباعة */
@media screen {
    .print-preview {
        max-width: 1200px;
        margin: 0 auto;
    }
}
</style>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><?php echo __('reports'); ?></h1>
    <div class="btn-toolbar mb-2 mb-md-0 no-print">
        <button type="button" class="btn btn-sm btn-primary me-2" id="printReport">
            <i class="fas fa-print"></i> طباعة التقرير
        </button>
        <button type="button" class="btn btn-sm btn-secondary me-2" id="quickPrint">
            <i class="fas fa-print"></i> طباعة سريعة
        </button>
        <?php if ($report_type == 'account_statement'): ?>
        <button type="button" class="btn btn-sm btn-info me-2" id="printAccountStatement">
            <i class="fas fa-file-pdf"></i> طباعة كشف الحساب
        </button>
        <?php endif; ?>
        <button type="button" class="btn btn-sm btn-success" id="exportExcel">
            <i class="fas fa-file-excel"></i> تصدير Excel
        </button>
    </div>
</div>

<!-- نموذج البحث والفلترة -->
<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0"><i class="fas fa-search me-2"></i><?php echo __('search_filter'); ?></h5>
    </div>
    <div class="card-body">
        <form method="GET" id="reportFilterForm">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <label for="report_type" class="form-label"><?php echo __('report_type'); ?></label>
                    <select class="form-select" id="report_type" name="report_type">
                        <option value="summary" <?php echo $report_type == 'summary' ? 'selected' : ''; ?>><?php echo __('summary_report'); ?></option>
                        <option value="account_statement" <?php echo $report_type == 'account_statement' ? 'selected' : ''; ?>>كشف حساب شامل</option>
                        <option value="sales_details" <?php echo $report_type == 'sales_details' ? 'selected' : ''; ?>><?php echo __('sales_details'); ?></option>
                        <option value="purchases_details" <?php echo $report_type == 'purchases_details' ? 'selected' : ''; ?>><?php echo __('purchases_details'); ?></option>
                        <option value="top_products" <?php echo $report_type == 'top_products' ? 'selected' : ''; ?>><?php echo __('top_products'); ?></option>
                        <option value="top_customers" <?php echo $report_type == 'top_customers' ? 'selected' : ''; ?>><?php echo __('top_customers'); ?></option>
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="start_date" class="form-label"><?php echo __('start_date'); ?></label>
                    <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo $start_date; ?>">
                </div>
                <div class="col-md-3 mb-3">
                    <label for="end_date" class="form-label"><?php echo __('end_date'); ?></label>
                    <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo $end_date; ?>">
                </div>
                <div class="col-md-3 mb-3">
                    <label for="customer_id" class="form-label"><?php echo __('customer'); ?></label>
                    <select class="form-select" id="customer_id" name="customer_id">
                        <option value="0"><?php echo __('all_customers'); ?></option>
                        <?php while ($customer = $customers->fetch_assoc()): ?>
                        <option value="<?php echo $customer['id']; ?>" <?php echo $customer_id == $customer['id'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($customer['name']); ?>
                        </option>
                        <?php endwhile; ?>
                    </select>
                </div>
            </div>
            <div class="row">
                <div class="col-12 text-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i> <?php echo __('search'); ?>
                    </button>
                    <a href="reports.php" class="btn btn-secondary">
                        <i class="fas fa-redo me-1"></i> <?php echo __('reset'); ?>
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- قسم التقرير الرئيسي -->
<div id="reportContent">
    <!-- عنصر الطباعة المحدد -->
    <div id="printableContent" class="d-none">
        <!-- سيتم ملء هذا العنصر بالمحتوى المطلوب للطباعة فقط -->
    </div>
    <?php if ($report_type == 'summary'): ?>
    <!-- تقرير ملخص -->
    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4 shadow-sm">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i><?php echo __('sales_report'); ?></h5>
                </div>
                <div class="card-body">
                    <table class="table table-striped">
                        <tr>
                            <th><?php echo __('period'); ?>:</th>
                            <td><?php echo date('d/m/Y', strtotime($start_date)) . ' - ' . date('d/m/Y', strtotime($end_date)); ?></td>
                        </tr>
                        <tr>
                            <th><?php echo __('invoice_count'); ?>:</th>
                            <td><?php echo $sales_stats['count']; ?></td>
                        </tr>
                        <tr>
                            <th><?php echo __('subtotal'); ?>:</th>
                            <td><?php echo number_format($sales_stats['subtotal'] ?? 0, 2) . ' ' . __('currency'); ?></td>
                        </tr>
                        <tr>
                            <th><?php echo __('tax_amount'); ?>:</th>
                            <td><?php echo number_format($sales_stats['tax'] ?? 0, 2) . ' ' . __('currency'); ?></td>
                        </tr>
                        <tr class="table-primary">
                            <th><?php echo __('total_sales'); ?>:</th>
                            <td class="fw-bold"><?php echo number_format($sales_stats['total'] ?? 0, 2) . ' ' . __('currency'); ?></td>
                        </tr>
                        <tr>
                            <th>إجمالي المدفوع:</th>
                            <td class="text-success"><?php echo number_format($sales_stats['total_paid'] ?? 0, 2) . ' ' . __('currency'); ?></td>
                        </tr>
                        <tr>
                            <th>إجمالي المتبقي:</th>
                            <td class="text-danger"><?php echo number_format($sales_stats['total_remaining'] ?? 0, 2) . ' ' . __('currency'); ?></td>
                        </tr>
                        <tr>
                            <th>مدفوع بالكامل:</th>
                            <td><span class="badge bg-success"><?php echo $sales_stats['paid_count'] ?? 0; ?></span></td>
                        </tr>
                        <tr>
                            <th>غير مدفوع:</th>
                            <td><span class="badge bg-danger"><?php echo $sales_stats['unpaid_count'] ?? 0; ?></span></td>
                        </tr>
                        <tr>
                            <th>مدفوع جزئياً:</th>
                            <td><span class="badge bg-warning"><?php echo $sales_stats['partial_count'] ?? 0; ?></span></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card mb-4 shadow-sm">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-shopping-cart me-2"></i><?php echo __('purchases_report'); ?></h5>
                </div>
                <div class="card-body">
                    <table class="table table-striped">
                        <tr>
                            <th><?php echo __('period'); ?>:</th>
                            <td><?php echo date('d/m/Y', strtotime($start_date)) . ' - ' . date('d/m/Y', strtotime($end_date)); ?></td>
                        </tr>
                        <tr>
                            <th><?php echo __('invoice_count'); ?>:</th>
                            <td><?php echo $purchases_stats['count']; ?></td>
                        </tr>
                        <tr>
                            <th><?php echo __('subtotal'); ?>:</th>
                            <td><?php echo number_format($purchases_stats['subtotal'] ?? 0, 2) . ' ' . __('currency'); ?></td>
                        </tr>
                        <tr>
                            <th><?php echo __('tax_amount'); ?>:</th>
                            <td><?php echo number_format($purchases_stats['tax'] ?? 0, 2) . ' ' . __('currency'); ?></td>
                        </tr>
                        <tr class="table-primary">
                            <th><?php echo __('total_purchases'); ?>:</th>
                            <td class="fw-bold"><?php echo number_format($purchases_stats['total'] ?? 0, 2) . ' ' . __('currency'); ?></td>
                        </tr>
                        <tr>
                            <th>إجمالي المدفوع:</th>
                            <td class="text-success"><?php echo number_format($purchases_stats['total_paid'] ?? 0, 2) . ' ' . __('currency'); ?></td>
                        </tr>
                        <tr>
                            <th>إجمالي المتبقي:</th>
                            <td class="text-danger"><?php echo number_format($purchases_stats['total_remaining'] ?? 0, 2) . ' ' . __('currency'); ?></td>
                        </tr>
                        <tr>
                            <th>مدفوع بالكامل:</th>
                            <td><span class="badge bg-success"><?php echo $purchases_stats['paid_count'] ?? 0; ?></span></td>
                        </tr>
                        <tr>
                            <th>غير مدفوع:</th>
                            <td><span class="badge bg-danger"><?php echo $purchases_stats['unpaid_count'] ?? 0; ?></span></td>
                        </tr>
                        <tr>
                            <th>مدفوع جزئياً:</th>
                            <td><span class="badge bg-warning"><?php echo $purchases_stats['partial_count'] ?? 0; ?></span></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>



    <div class="row">
        <div class="col-md-12">
            <div class="card mb-4 shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i><?php echo __('profit_loss_report'); ?></h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div id="profitLossChartContainer">
                                <canvas id="profitLossChart" height="200"></canvas>
                            </div>
                            <div id="noProfitLossDataMessage" class="text-center py-5 d-none">
                                <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
                                <p class="lead"><?php echo __('no_data_available'); ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-striped">
                                <tr class="table-success">
                                    <th><?php echo __('total_sales'); ?>:</th>
                                    <td><?php echo number_format($sales_stats['total'] ?? 0, 2) . ' ' . __('currency'); ?></td>
                                </tr>
                                <tr class="table-danger">
                                    <th><?php echo __('total_purchases'); ?>:</th>
                                    <td><?php echo number_format($purchases_stats['total'] ?? 0, 2) . ' ' . __('currency'); ?></td>
                                </tr>
                                <tr class="<?php echo ($sales_stats['total'] - $purchases_stats['total'] >= 0) ? 'table-success' : 'table-danger'; ?>">
                                    <th><?php echo __('profit_loss'); ?>:</th>
                                    <td class="fw-bold"><?php echo number_format(($sales_stats['total'] ?? 0) - ($purchases_stats['total'] ?? 0), 2) . ' ' . __('currency'); ?></td>
                                </tr>
                                <tr>
                                    <th><?php echo __('total_tax_collected'); ?>:</th>
                                    <td><?php echo number_format($sales_stats['tax'] ?? 0, 2) . ' ' . __('currency'); ?></td>
                                </tr>
                                <tr>
                                    <th><?php echo __('total_tax_paid'); ?>:</th>
                                    <td><?php echo number_format($purchases_stats['tax'] ?? 0, 2) . ' ' . __('currency'); ?></td>
                                </tr>
                                <tr class="table-info">
                                    <th><?php echo __('net_tax'); ?>:</th>
                                    <td><?php echo number_format(($sales_stats['tax'] ?? 0) - ($purchases_stats['tax'] ?? 0), 2) . ' ' . __('currency'); ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <?php if ($report_type == 'account_statement'): ?>
    <!-- كشف الحساب الشامل -->
    <div class="card mb-4 shadow-sm">
        <div class="card-header bg-dark text-white">
            <h5 class="mb-0">
                <i class="fas fa-file-invoice me-2"></i>
                كشف الحساب الشامل
                <?php if ($customer_id > 0): ?>
                    <?php
                    $customer_name_query = $db->query("SELECT name FROM customers WHERE id = $customer_id");
                    $customer_name = $customer_name_query ? $customer_name_query->fetch_assoc()['name'] : '';
                    ?>
                    - <?php echo htmlspecialchars($customer_name); ?>
                <?php endif; ?>
            </h5>
            <small class="text-light">
                الفترة: <?php echo date('d/m/Y', strtotime($start_date)) . ' - ' . date('d/m/Y', strtotime($end_date)); ?>
            </small>
        </div>
        <div class="card-body p-0">
            <!-- ملخص الرصيد -->
            <div class="row g-0 border-bottom">
                <div class="col-md-2 bg-light p-3 border-end">
                    <div class="text-center">
                        <h6 class="text-success mb-1">إجمالي المبيعات</h6>
                        <h5 class="text-success mb-0"><?php echo number_format($sales_stats['total'] ?? 0, 2); ?> ر.س</h5>
                    </div>
                </div>
                <div class="col-md-2 bg-light p-3 border-end">
                    <div class="text-center">
                        <h6 class="text-danger mb-1">إجمالي المشتريات</h6>
                        <h5 class="text-danger mb-0"><?php echo number_format($purchases_stats['total'] ?? 0, 2); ?> ر.س</h5>
                    </div>
                </div>
                <div class="col-md-2 bg-light p-3 border-end">
                    <div class="text-center">
                        <?php $net_balance = ($sales_stats['total'] ?? 0) - ($purchases_stats['total'] ?? 0); ?>
                        <h6 class="<?php echo $net_balance >= 0 ? 'text-success' : 'text-danger'; ?> mb-1">صافي الرصيد</h6>
                        <h5 class="<?php echo $net_balance >= 0 ? 'text-success' : 'text-danger'; ?> mb-0">
                            <?php echo number_format($net_balance, 2); ?> ر.س
                        </h5>
                    </div>
                </div>
                <div class="col-md-2 bg-light p-3 border-end">
                    <div class="text-center">
                        <h6 class="text-primary mb-1">ضريبة المبيعات</h6>
                        <h5 class="text-primary mb-0"><?php echo number_format($sales_stats['tax'] ?? 0, 2); ?> ر.س</h5>
                    </div>
                </div>
                <div class="col-md-2 bg-light p-3 border-end">
                    <div class="text-center">
                        <h6 class="text-warning mb-1">ضريبة المشتريات</h6>
                        <h5 class="text-warning mb-0"><?php echo number_format($purchases_stats['tax'] ?? 0, 2); ?> ر.س</h5>
                    </div>
                </div>
                <div class="col-md-2 bg-light p-3">
                    <div class="text-center">
                        <?php $net_tax = ($sales_stats['tax'] ?? 0) - ($purchases_stats['tax'] ?? 0); ?>
                        <h6 class="<?php echo $net_tax >= 0 ? 'text-success' : 'text-danger'; ?> mb-1">صافي الضريبة</h6>
                        <h5 class="<?php echo $net_tax >= 0 ? 'text-success' : 'text-danger'; ?> mb-0">
                            <?php echo number_format($net_tax, 2); ?> ر.س
                        </h5>
                    </div>
                </div>
            </div>

            <!-- جدول المعاملات -->
            <div class="table-responsive">
                <table class="table table-hover mb-0" style="font-size: 14px;">
                    <thead class="table-dark">
                        <tr>
                            <th style="width: 70px;">التاريخ</th>
                            <th style="width: 100px;">رقم الفاتورة</th>
                            <th style="width: 80px;">نوع المعاملة</th>
                            <th>العميل/المورد</th>
                            <th style="width: 80px;">حالة الدفع</th>
                            <th style="width: 90px;" class="text-end">مدين</th>
                            <th style="width: 90px;" class="text-end">دائن</th>
                            <th style="width: 70px;" class="text-end">ض.مدين</th>
                            <th style="width: 70px;" class="text-end">ض.دائن</th>
                            <th style="width: 90px;" class="text-end">الرصيد</th>
                            <th style="width: 70px;">إجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (!empty($account_transactions)): ?>
                            <?php foreach ($account_transactions as $transaction): ?>
                                <?php
                                // التحقق المزدوج من نوع المعاملة مع تسجيل للتشخيص
                                $transaction_type_clean = strtolower(trim($transaction['transaction_type']));
                                $is_sale = ($transaction_type_clean === 'sale');
                                $is_purchase = ($transaction_type_clean === 'purchase');

                                // تشخيص إضافي للتأكد من صحة البيانات
                                if (!$is_sale && !$is_purchase) {
                                    error_log("Unknown transaction type detected: '" . $transaction['transaction_type'] . "' for ID: " . $transaction['id']);
                                }
                                ?>
                                <tr class="<?php echo $is_sale ? 'table-light' : 'table-warning'; ?>"
                                    data-transaction-type="<?php echo htmlspecialchars($transaction['transaction_type']); ?>"
                                    data-transaction-id="<?php echo $transaction['id']; ?>">
                                    <td>
                                        <small class="fw-bold">
                                            <?php echo date('d/m/Y', strtotime($transaction['date'])); ?>
                                        </small>
                                        <br>
                                        <small class="text-muted">ID: <?php echo $transaction['id']; ?></small>
                                    </td>
                                    <td>
                                        <span class="badge <?php echo $is_sale ? 'bg-success' : 'bg-danger'; ?>">
                                            <?php echo htmlspecialchars($transaction['invoice_number']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge <?php echo $is_sale ? 'bg-primary' : 'bg-warning'; ?>">
                                            <i class="fas <?php echo $is_sale ? 'fa-arrow-up' : 'fa-arrow-down'; ?> me-1"></i>
                                            <?php
                                            // التأكد من عرض النوع الصحيح
                                            if ($is_sale) {
                                                echo 'مبيعات';
                                            } elseif ($is_purchase) {
                                                echo 'مشتريات';
                                            } else {
                                                echo htmlspecialchars($transaction['transaction_type_ar']);
                                            }
                                            ?>
                                        </span>
                                        <br>
                                        <small class="text-muted"><?php echo htmlspecialchars($transaction['transaction_type']); ?></small>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas <?php echo $transaction['transaction_type'] == 'sale' ? 'fa-user' : 'fa-truck'; ?> me-2 text-muted"></i>
                                            <span><?php echo htmlspecialchars($transaction['customer_name'] ?? 'غير محدد'); ?></span>
                                        </div>
                                    </td>
                                    <td>
                                        <?php
                                        $status_class = '';
                                        $status_text = '';
                                        switch ($transaction['payment_status']) {
                                            case 'paid':
                                                $status_class = 'bg-success';
                                                $status_text = 'مدفوع';
                                                break;
                                            case 'unpaid':
                                                $status_class = 'bg-danger';
                                                $status_text = 'غير مدفوع';
                                                break;
                                            case 'partial':
                                                $status_class = 'bg-warning';
                                                $status_text = 'مدفوع جزئياً';
                                                break;
                                            default:
                                                $status_class = 'bg-secondary';
                                                $status_text = 'غير محدد';
                                        }
                                        ?>
                                        <span class="badge <?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                                    </td>
                                    <!-- عمود المدين (المشتريات) -->
                                    <td class="text-end">
                                        <?php if ($is_purchase): ?>
                                            <span class="text-danger fw-bold">
                                                <?php echo number_format($transaction['subtotal'] ?? $transaction['total_amount'], 2); ?> ر.س
                                            </span>
                                            <br>
                                            <small class="text-muted">مشتريات</small>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <!-- عمود الدائن (المبيعات) -->
                                    <td class="text-end">
                                        <?php if ($is_sale): ?>
                                            <span class="text-success fw-bold">
                                                <?php echo number_format($transaction['subtotal'] ?? $transaction['total_amount'], 2); ?> ر.س
                                            </span>
                                            <br>
                                            <small class="text-muted">مبيعات</small>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <!-- عمود ضريبة المدين (ضريبة المشتريات) -->
                                    <td class="text-end">
                                        <?php if ($is_purchase): ?>
                                            <span class="text-warning fw-bold">
                                                <?php echo number_format($transaction['tax_amount'] ?? 0, 2); ?> ر.س
                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <!-- عمود ضريبة الدائن (ضريبة المبيعات) -->
                                    <td class="text-end">
                                        <?php if ($is_sale): ?>
                                            <span class="text-primary fw-bold">
                                                <?php echo number_format($transaction['tax_amount'] ?? 0, 2); ?> ر.س
                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <!-- عمود الرصيد التراكمي -->
                                    <td class="text-end">
                                        <span class="fw-bold <?php echo $transaction['running_balance'] >= 0 ? 'text-success' : 'text-danger'; ?>">
                                            <?php echo number_format($transaction['running_balance'], 2); ?> ر.س
                                        </span>
                                        <br>
                                        <small class="text-muted">
                                            (<?php echo $transaction['balance_change'] ?? ''; ?>)
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo $transaction['transaction_type'] == 'sale' ? 'view_sale.php' : 'view_purchase.php'; ?>?id=<?php echo $transaction['id']; ?>"
                                               class="btn btn-sm btn-outline-info" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="print_invoice.php?id=<?php echo $transaction['id']; ?>&type=<?php echo $transaction['transaction_type']; ?>"
                                               class="btn btn-sm btn-outline-secondary" target="_blank" title="طباعة">
                                                <i class="fas fa-print"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="11" class="text-center py-5">
                                    <div class="text-muted">
                                        <i class="fas fa-file-invoice fa-3x mb-3"></i>
                                        <p class="lead">لا توجد معاملات في الفترة المحددة</p>
                                        <small>جرب تغيير الفترة الزمنية أو العميل</small>
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                    <?php if (!empty($account_transactions)): ?>
                    <tfoot class="table-dark">
                        <tr>
                            <th colspan="5" class="text-end">الإجمالي:</th>
                            <th class="text-end">
                                <?php
                                // إجمالي المشتريات (مدين)
                                $purchase_transactions = array_filter($account_transactions, function($t) {
                                    return strtolower(trim($t['transaction_type'])) === 'purchase';
                                });
                                $total_debit = 0;
                                foreach ($purchase_transactions as $pt) {
                                    $total_debit += floatval($pt['subtotal'] ?? $pt['total_amount']);
                                }
                                echo number_format($total_debit, 2);
                                ?> ر.س
                                <br>
                                <small class="text-muted">(<?php echo count($purchase_transactions); ?> فاتورة)</small>
                            </th>
                            <th class="text-end">
                                <?php
                                // إجمالي المبيعات (دائن)
                                $sale_transactions = array_filter($account_transactions, function($t) {
                                    return strtolower(trim($t['transaction_type'])) === 'sale';
                                });
                                $total_credit = 0;
                                foreach ($sale_transactions as $st) {
                                    $total_credit += floatval($st['subtotal'] ?? $st['total_amount']);
                                }
                                echo number_format($total_credit, 2);
                                ?> ر.س
                                <br>
                                <small class="text-muted">(<?php echo count($sale_transactions); ?> فاتورة)</small>
                            </th>
                            <th class="text-end">
                                <?php
                                // إجمالي ضريبة المشتريات
                                $total_purchase_tax = 0;
                                foreach ($purchase_transactions as $pt) {
                                    $total_purchase_tax += floatval($pt['tax_amount'] ?? 0);
                                }
                                echo number_format($total_purchase_tax, 2);
                                ?> ر.س
                            </th>
                            <th class="text-end">
                                <?php
                                // إجمالي ضريبة المبيعات
                                $total_sale_tax = 0;
                                foreach ($sale_transactions as $st) {
                                    $total_sale_tax += floatval($st['tax_amount'] ?? 0);
                                }
                                echo number_format($total_sale_tax, 2);
                                ?> ر.س
                            </th>
                            <th class="text-end">
                                <span class="fw-bold <?php echo ($total_credit - $total_debit) >= 0 ? 'text-success' : 'text-danger'; ?>">
                                    <?php echo number_format($total_credit - $total_debit, 2); ?> ر.س
                                </span>
                            </th>
                            <th></th>
                        </tr>
                    </tfoot>
                    <?php endif; ?>
                </table>
            </div>

            <!-- معلومات الطباعة -->
            <div class="print-info" style="display: none;">
                <p>
                    تاريخ الطباعة: <?php echo date('d/m/Y H:i'); ?> |
                    المستخدم: <?php echo $_SESSION['username'] ?? 'غير محدد'; ?> |
                    الفترة: <?php echo date('d/m/Y', strtotime($start_date)) . ' - ' . date('d/m/Y', strtotime($end_date)); ?>
                    <?php if ($customer_id > 0): ?>
                        <?php
                        $customer_name_query = $db->query("SELECT name FROM customers WHERE id = $customer_id");
                        $customer_name = $customer_name_query ? $customer_name_query->fetch_assoc()['name'] : '';
                        ?>
                        | العميل: <?php echo htmlspecialchars($customer_name); ?>
                    <?php endif; ?>
                </p>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <?php if ($report_type == 'sales_details'): ?>
    <!-- تقرير تفاصيل المبيعات -->
    <div class="card mb-4 shadow-sm">
        <div class="card-header bg-success text-white">
            <h5 class="mb-0"><i class="fas fa-file-invoice-dollar me-2"></i><?php echo __('sales_details'); ?></h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th><?php echo __('invoice_number'); ?></th>
                            <th><?php echo __('date'); ?></th>
                            <th><?php echo __('customer'); ?></th>
                            <th><?php echo __('total'); ?></th>
                            <th>طريقة الدفع</th>
                            <th>حالة الدفع</th>
                            <th>المدفوع</th>
                            <th>المتبقي</th>
                            <th><?php echo __('actions'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if ($sales_result && $sales_result->num_rows > 0): ?>
                            <?php while ($row = $sales_result->fetch_assoc()):
                                // ترجمة طريقة الدفع
                                $payment_methods = [
                                    'cash' => 'نقدي',
                                    'card' => 'بطاقة ائتمان',
                                    'bank_transfer' => 'تحويل بنكي',
                                    'check' => 'شيك',
                                    'installment' => 'تقسيط',
                                    'other' => 'أخرى'
                                ];

                                // ترجمة حالة الدفع
                                $payment_statuses = [
                                    'paid' => 'مدفوع بالكامل',
                                    'partial' => 'مدفوع جزئياً',
                                    'unpaid' => 'غير مدفوع'
                                ];

                                // ألوان حالة الدفع
                                $status_colors = [
                                    'paid' => 'success',
                                    'partial' => 'warning',
                                    'unpaid' => 'danger'
                                ];
                            ?>
                                <tr>
                                    <td><?php echo $row['id']; ?></td>
                                    <td><?php echo $row['invoice_number']; ?></td>
                                    <td><?php echo date('d/m/Y', strtotime($row['date'])); ?></td>
                                    <td><?php echo htmlspecialchars($row['customer_name'] ?? __('no_customer')); ?></td>
                                    <td><?php echo number_format($row['total_amount'], 2) . ' ' . __('currency'); ?></td>
                                    <td>
                                        <span class="badge bg-secondary">
                                            <?php echo $payment_methods[$row['payment_method'] ?? 'cash'] ?? ($row['payment_method'] ?? 'نقدي'); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo $status_colors[$row['payment_status'] ?? 'unpaid'] ?? 'secondary'; ?>">
                                            <?php echo $payment_statuses[$row['payment_status'] ?? 'unpaid'] ?? ($row['payment_status'] ?? 'غير مدفوع'); ?>
                                        </span>
                                    </td>
                                    <td><?php echo number_format($row['paid_amount'] ?? 0, 2) . ' ' . __('currency'); ?></td>
                                    <td>
                                        <?php if (($row['remaining_amount'] ?? 0) > 0): ?>
                                            <span class="text-danger fw-bold"><?php echo number_format($row['remaining_amount'], 2) . ' ' . __('currency'); ?></span>
                                        <?php else: ?>
                                            <span class="text-success">0.00 <?php echo __('currency'); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <a href="view_sale.php?id=<?php echo $row['id']; ?>" class="btn btn-sm btn-info" title="<?php echo __('view'); ?>">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="print_invoice.php?id=<?php echo $row['id']; ?>&type=sale" class="btn btn-sm btn-secondary" target="_blank" title="<?php echo __('print'); ?>">
                                            <i class="fas fa-print"></i>
                                        </a>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="10" class="text-center"><?php echo __('no_data'); ?></td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                    <tfoot>
                        <tr class="table-primary">
                            <th colspan="4"><?php echo __('total'); ?></th>
                            <th><?php echo number_format($sales_stats['total'] ?? 0, 2) . ' ' . __('currency'); ?></th>
                            <th colspan="2">إجمالي المدفوعات</th>
                            <th><?php echo number_format($sales_stats['total_paid'] ?? 0, 2) . ' ' . __('currency'); ?></th>
                            <th class="text-danger"><?php echo number_format($sales_stats['total_remaining'] ?? 0, 2) . ' ' . __('currency'); ?></th>
                            <th></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <?php if ($report_type == 'purchases_details'): ?>
    <!-- تقرير تفاصيل المشتريات -->
    <div class="card mb-4 shadow-sm">
        <div class="card-header bg-info text-white">
            <h5 class="mb-0"><i class="fas fa-shopping-cart me-2"></i><?php echo __('purchases_details'); ?></h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th><?php echo __('invoice_number'); ?></th>
                            <th><?php echo __('date'); ?></th>
                            <th><?php echo __('customer'); ?></th>
                            <th><?php echo __('total'); ?></th>
                            <th>طريقة الدفع</th>
                            <th>حالة الدفع</th>
                            <th>المدفوع</th>
                            <th>المتبقي</th>
                            <th><?php echo __('actions'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if ($purchases_result && $purchases_result->num_rows > 0): ?>
                            <?php while ($row = $purchases_result->fetch_assoc()):
                                // ترجمة طريقة الدفع
                                $payment_methods = [
                                    'cash' => 'نقدي',
                                    'card' => 'بطاقة ائتمان',
                                    'bank_transfer' => 'تحويل بنكي',
                                    'check' => 'شيك',
                                    'installment' => 'تقسيط',
                                    'other' => 'أخرى'
                                ];

                                // ترجمة حالة الدفع
                                $payment_statuses = [
                                    'paid' => 'مدفوع بالكامل',
                                    'partial' => 'مدفوع جزئياً',
                                    'unpaid' => 'غير مدفوع'
                                ];

                                // ألوان حالة الدفع
                                $status_colors = [
                                    'paid' => 'success',
                                    'partial' => 'warning',
                                    'unpaid' => 'danger'
                                ];
                            ?>
                                <tr>
                                    <td><?php echo $row['id']; ?></td>
                                    <td><?php echo $row['invoice_number']; ?></td>
                                    <td><?php echo date('d/m/Y', strtotime($row['date'])); ?></td>
                                    <td><?php echo htmlspecialchars($row['customer_name'] ?? __('no_customer')); ?></td>
                                    <td><?php echo number_format($row['total_amount'], 2) . ' ' . __('currency'); ?></td>
                                    <td>
                                        <span class="badge bg-secondary">
                                            <?php echo $payment_methods[$row['payment_method'] ?? 'cash'] ?? ($row['payment_method'] ?? 'نقدي'); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo $status_colors[$row['payment_status'] ?? 'unpaid'] ?? 'secondary'; ?>">
                                            <?php echo $payment_statuses[$row['payment_status'] ?? 'unpaid'] ?? ($row['payment_status'] ?? 'غير مدفوع'); ?>
                                        </span>
                                    </td>
                                    <td><?php echo number_format($row['paid_amount'] ?? 0, 2) . ' ' . __('currency'); ?></td>
                                    <td>
                                        <?php if (($row['remaining_amount'] ?? 0) > 0): ?>
                                            <span class="text-danger fw-bold"><?php echo number_format($row['remaining_amount'], 2) . ' ' . __('currency'); ?></span>
                                        <?php else: ?>
                                            <span class="text-success">0.00 <?php echo __('currency'); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <a href="view_purchase.php?id=<?php echo $row['id']; ?>" class="btn btn-sm btn-info" title="<?php echo __('view'); ?>">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="print_invoice.php?id=<?php echo $row['id']; ?>&type=purchase" class="btn btn-sm btn-secondary" target="_blank" title="<?php echo __('print'); ?>">
                                            <i class="fas fa-print"></i>
                                        </a>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="10" class="text-center"><?php echo __('no_data'); ?></td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                    <tfoot>
                        <tr class="table-primary">
                            <th colspan="4"><?php echo __('total'); ?></th>
                            <th><?php echo number_format($purchases_stats['total'] ?? 0, 2) . ' ' . __('currency'); ?></th>
                            <th colspan="2">إجمالي المدفوعات</th>
                            <th><?php echo number_format($purchases_stats['total_paid'] ?? 0, 2) . ' ' . __('currency'); ?></th>
                            <th class="text-danger"><?php echo number_format($purchases_stats['total_remaining'] ?? 0, 2) . ' ' . __('currency'); ?></th>
                            <th></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <?php if ($report_type == 'top_products'): ?>
    <!-- تقرير المنتجات الأكثر مبيعًا -->
    <div class="card mb-4 shadow-sm">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-box me-2"></i><?php echo __('top_products'); ?></h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-8">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th><?php echo __('product_name'); ?></th>
                                    <th><?php echo __('quantity_sold'); ?></th>
                                    <th><?php echo __('total_amount'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if ($top_products_result && $top_products_result->num_rows > 0): ?>
                                    <?php $counter = 1; ?>
                                    <?php while ($row = $top_products_result->fetch_assoc()): ?>
                                        <tr>
                                            <td><?php echo $counter++; ?></td>
                                            <td><?php echo htmlspecialchars($row['product_name']); ?></td>
                                            <td><?php echo $row['total_quantity']; ?></td>
                                            <td><?php echo number_format($row['total_amount'], 2) . ' ' . __('currency'); ?></td>
                                        </tr>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="4" class="text-center"><?php echo __('no_data'); ?></td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="col-md-4">
                    <div id="topProductsChartContainer">
                        <canvas id="topProductsChart" height="300"></canvas>
                    </div>
                    <div id="noTopProductsDataMessage" class="text-center py-5 d-none">
                        <i class="fas fa-box fa-3x text-muted mb-3"></i>
                        <p class="lead"><?php echo __('no_data_available'); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <?php if ($report_type == 'top_customers'): ?>
    <!-- تقرير العملاء الأكثر شراءً -->
    <div class="card mb-4 shadow-sm">
        <div class="card-header bg-success text-white">
            <h5 class="mb-0"><i class="fas fa-users me-2"></i><?php echo __('top_customers'); ?></h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-8">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th><?php echo __('customer_name'); ?></th>
                                    <th><?php echo __('invoice_count'); ?></th>
                                    <th><?php echo __('total_amount'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if ($top_customers_result && $top_customers_result->num_rows > 0): ?>
                                    <?php $counter = 1; ?>
                                    <?php while ($row = $top_customers_result->fetch_assoc()): ?>
                                        <tr>
                                            <td><?php echo $counter++; ?></td>
                                            <td><?php echo htmlspecialchars($row['customer_name']); ?></td>
                                            <td><?php echo $row['invoice_count']; ?></td>
                                            <td><?php echo number_format($row['total_amount'], 2) . ' ' . __('currency'); ?></td>
                                        </tr>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="4" class="text-center"><?php echo __('no_data'); ?></td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="col-md-4">
                    <div id="topCustomersChartContainer">
                        <canvas id="topCustomersChart" height="300"></canvas>
                    </div>
                    <div id="noTopCustomersDataMessage" class="text-center py-5 d-none">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <p class="lead"><?php echo __('no_data_available'); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const isRTL = document.documentElement.dir === 'rtl';
    const reportType = document.getElementById('report_type').value;

    // جلب بيانات التقارير
    if (reportType === 'summary') {
        // رسم بياني للأرباح والخسائر
        const salesTotal = <?php echo $sales_stats['total'] ?? 0; ?>;
        const purchasesTotal = <?php echo $purchases_stats['total'] ?? 0; ?>;
        const profitLoss = salesTotal - purchasesTotal;

        // التحقق من وجود بيانات
        const hasProfitLossData = salesTotal > 0 || purchasesTotal > 0;

        if (hasProfitLossData) {
            // إظهار الرسم البياني وإخفاء رسالة عدم وجود بيانات
            document.getElementById('profitLossChartContainer').classList.remove('d-none');
            document.getElementById('noProfitLossDataMessage').classList.add('d-none');

            const profitLossCtx = document.getElementById('profitLossChart').getContext('2d');
            const profitLossData = {
                labels: ['<?php echo __('sales'); ?>', '<?php echo __('purchases'); ?>', '<?php echo __('profit_loss'); ?>'],
                datasets: [{
                    data: [
                        salesTotal,
                        purchasesTotal,
                        profitLoss
                    ],
                    backgroundColor: [
                        'rgba(40, 167, 69, 0.7)',
                        'rgba(220, 53, 69, 0.7)',
                        <?php echo (($sales_stats['total'] ?? 0) - ($purchases_stats['total'] ?? 0) >= 0) ? "'rgba(40, 167, 69, 0.7)'" : "'rgba(220, 53, 69, 0.7)'"; ?>
                    ],
                    borderColor: [
                        'rgba(40, 167, 69, 1)',
                        'rgba(220, 53, 69, 1)',
                        <?php echo (($sales_stats['total'] ?? 0) - ($purchases_stats['total'] ?? 0) >= 0) ? "'rgba(40, 167, 69, 1)'" : "'rgba(220, 53, 69, 1)'"; ?>
                    ],
                    borderWidth: 1
                }]
            };

            new Chart(profitLossCtx, {
                type: 'bar',
                data: profitLossData,
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        },
                        title: {
                            display: true,
                            text: '<?php echo __('profit_loss_report'); ?>',
                            rtl: isRTL
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed.y !== null) {
                                        label += new Intl.NumberFormat('<?php echo $lang_code === 'ar' ? 'ar-SA' : 'en-US'; ?>', {
                                            style: 'currency',
                                            currency: 'SAR'
                                        }).format(context.parsed.y);
                                    }
                                    return label;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        } else {
            // إخفاء الرسم البياني وإظهار رسالة عدم وجود بيانات
            document.getElementById('profitLossChartContainer').classList.add('d-none');
            document.getElementById('noProfitLossDataMessage').classList.remove('d-none');
        }
    }

    // رسم بياني للمنتجات الأكثر مبيعًا
    if (reportType === 'top_products' && document.getElementById('topProductsChart')) {
        const productNames = [];
        const productQuantities = [];

        <?php if (isset($top_products_result) && $top_products_result->num_rows > 0): ?>
            <?php $top_products_result->data_seek(0); ?>
            <?php while ($row = $top_products_result->fetch_assoc()): ?>
                productNames.push('<?php echo addslashes($row['product_name']); ?>');
                productQuantities.push(<?php echo $row['total_quantity']; ?>);
            <?php endwhile; ?>
        <?php endif; ?>

        // التحقق من وجود بيانات
        const hasProductsData = productNames.length > 0 && productQuantities.some(qty => qty > 0);

        if (hasProductsData) {
            // إظهار الرسم البياني وإخفاء رسالة عدم وجود بيانات
            document.getElementById('topProductsChartContainer').classList.remove('d-none');
            document.getElementById('noTopProductsDataMessage').classList.add('d-none');

            const topProductsCtx = document.getElementById('topProductsChart').getContext('2d');
            new Chart(topProductsCtx, {
                type: 'pie',
                data: {
                    labels: productNames,
                    datasets: [{
                        data: productQuantities,
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.7)',
                            'rgba(54, 162, 235, 0.7)',
                            'rgba(255, 206, 86, 0.7)',
                            'rgba(75, 192, 192, 0.7)',
                            'rgba(153, 102, 255, 0.7)',
                            'rgba(255, 159, 64, 0.7)',
                            'rgba(199, 199, 199, 0.7)',
                            'rgba(83, 102, 255, 0.7)',
                            'rgba(40, 159, 64, 0.7)',
                            'rgba(210, 199, 199, 0.7)'
                        ],
                        borderColor: [
                            'rgba(255, 99, 132, 1)',
                            'rgba(54, 162, 235, 1)',
                            'rgba(255, 206, 86, 1)',
                            'rgba(75, 192, 192, 1)',
                            'rgba(153, 102, 255, 1)',
                            'rgba(255, 159, 64, 1)',
                            'rgba(199, 199, 199, 1)',
                            'rgba(83, 102, 255, 1)',
                            'rgba(40, 159, 64, 1)',
                            'rgba(210, 199, 199, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'right',
                            rtl: isRTL
                        },
                        title: {
                            display: true,
                            text: '<?php echo __('top_products'); ?>',
                            rtl: isRTL
                        }
                    }
                }
            });
        } else {
            // إخفاء الرسم البياني وإظهار رسالة عدم وجود بيانات
            document.getElementById('topProductsChartContainer').classList.add('d-none');
            document.getElementById('noTopProductsDataMessage').classList.remove('d-none');
        }
    }

    // رسم بياني للعملاء الأكثر شراءً
    if (reportType === 'top_customers' && document.getElementById('topCustomersChart')) {
        const customerNames = [];
        const customerAmounts = [];

        <?php if (isset($top_customers_result) && $top_customers_result->num_rows > 0): ?>
            <?php $top_customers_result->data_seek(0); ?>
            <?php while ($row = $top_customers_result->fetch_assoc()): ?>
                customerNames.push('<?php echo addslashes($row['customer_name']); ?>');
                customerAmounts.push(<?php echo $row['total_amount']; ?>);
            <?php endwhile; ?>
        <?php endif; ?>

        // التحقق من وجود بيانات
        const hasCustomersData = customerNames.length > 0 && customerAmounts.some(amount => amount > 0);

        if (hasCustomersData) {
            // إظهار الرسم البياني وإخفاء رسالة عدم وجود بيانات
            document.getElementById('topCustomersChartContainer').classList.remove('d-none');
            document.getElementById('noTopCustomersDataMessage').classList.add('d-none');

            const topCustomersCtx = document.getElementById('topCustomersChart').getContext('2d');
            new Chart(topCustomersCtx, {
                type: 'doughnut',
                data: {
                    labels: customerNames,
                    datasets: [{
                        data: customerAmounts,
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.7)',
                            'rgba(54, 162, 235, 0.7)',
                            'rgba(255, 206, 86, 0.7)',
                            'rgba(75, 192, 192, 0.7)',
                            'rgba(153, 102, 255, 0.7)',
                            'rgba(255, 159, 64, 0.7)',
                            'rgba(199, 199, 199, 0.7)',
                            'rgba(83, 102, 255, 0.7)',
                            'rgba(40, 159, 64, 0.7)',
                            'rgba(210, 199, 199, 0.7)'
                        ],
                        borderColor: [
                            'rgba(255, 99, 132, 1)',
                            'rgba(54, 162, 235, 1)',
                            'rgba(255, 206, 86, 1)',
                            'rgba(75, 192, 192, 1)',
                            'rgba(153, 102, 255, 1)',
                            'rgba(255, 159, 64, 1)',
                            'rgba(199, 199, 199, 1)',
                            'rgba(83, 102, 255, 1)',
                            'rgba(40, 159, 64, 1)',
                            'rgba(210, 199, 199, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'right',
                            rtl: isRTL
                        },
                        title: {
                            display: true,
                            text: '<?php echo __('top_customers'); ?>',
                            rtl: isRTL
                        }
                    }
                }
            });
        } else {
            // إخفاء الرسم البياني وإظهار رسالة عدم وجود بيانات
            document.getElementById('topCustomersChartContainer').classList.add('d-none');
            document.getElementById('noTopCustomersDataMessage').classList.remove('d-none');
        }
    }

    // طباعة التقرير
    document.getElementById('printReport').addEventListener('click', function(e) {
        e.preventDefault();

        // الحصول على محتوى التقرير
        const reportContent = document.getElementById('reportContent').innerHTML;
        const reportTitle = document.querySelector('h1.h2').textContent;

        // إرسال محتوى التقرير إلى الخادم
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'print_report_handler.php';
        form.target = '_blank';

        // إضافة حقول النموذج
        const contentField = document.createElement('input');
        contentField.type = 'hidden';
        contentField.name = 'report_content';
        contentField.value = reportContent;
        form.appendChild(contentField);

        const titleField = document.createElement('input');
        titleField.type = 'hidden';
        titleField.name = 'report_title';
        titleField.value = reportTitle;
        form.appendChild(titleField);

        const startDateField = document.createElement('input');
        startDateField.type = 'hidden';
        startDateField.name = 'start_date';
        startDateField.value = '<?php echo $start_date; ?>';
        form.appendChild(startDateField);

        const endDateField = document.createElement('input');
        endDateField.type = 'hidden';
        endDateField.name = 'end_date';
        endDateField.value = '<?php echo $end_date; ?>';
        form.appendChild(endDateField);

        const reportTypeField = document.createElement('input');
        reportTypeField.type = 'hidden';
        reportTypeField.name = 'report_type';
        reportTypeField.value = '<?php echo $report_type; ?>';
        form.appendChild(reportTypeField);

        const customerIdField = document.createElement('input');
        customerIdField.type = 'hidden';
        customerIdField.name = 'customer_id';
        customerIdField.value = '<?php echo $customer_id; ?>';
        form.appendChild(customerIdField);

        // إضافة النموذج إلى المستند وإرساله
        document.body.appendChild(form);
        form.submit();
        document.body.removeChild(form);
    });
});
</script>

<!-- تضمين ملف JavaScript المحسن للطباعة -->
<script src="assets/js/print.js"></script>

<!-- JavaScript للطباعة -->
<script>
// طباعة التقرير العام محسنة
document.getElementById('printReport').addEventListener('click', function() {
    preparePrintContent();
    printReport('<?php echo $report_type; ?>');
});

// طباعة سريعة
document.getElementById('quickPrint').addEventListener('click', function() {
    preparePrintContent();
    quickPrint();
});

/**
 * تحضير المحتوى للطباعة
 */
function preparePrintContent() {
    const reportType = '<?php echo $report_type; ?>';
    const printableContent = document.getElementById('printableContent');

    // مسح المحتوى السابق
    printableContent.innerHTML = '';

    // إنشاء رأس التقرير
    const reportHeader = createReportHeader(reportType);
    printableContent.appendChild(reportHeader);

    if (reportType === 'account_statement') {
        // كشف الحساب - نسخ الجدول فقط
        const accountTable = document.querySelector('#reportContent .table-responsive');
        if (accountTable) {
            const clonedTable = accountTable.cloneNode(true);
            // إزالة أزرار الإجراءات
            const actionButtons = clonedTable.querySelectorAll('.btn-group, .btn');
            actionButtons.forEach(btn => btn.remove());
            printableContent.appendChild(clonedTable);
        }

        // إضافة ملخص الرصيد
        const balanceSummary = document.querySelector('#reportContent .row.g-0.border-bottom');
        if (balanceSummary) {
            const clonedSummary = balanceSummary.cloneNode(true);
            printableContent.insertBefore(clonedSummary, printableContent.firstChild.nextSibling);
        }
    } else if (reportType === 'summary') {
        // تقرير الملخص - نسخ الجداول والإحصائيات فقط
        const summaryCards = document.querySelectorAll('#reportContent .card .table');
        summaryCards.forEach(table => {
            const clonedTable = table.cloneNode(true);
            const wrapper = document.createElement('div');
            wrapper.className = 'mb-4';
            wrapper.appendChild(clonedTable);
            printableContent.appendChild(wrapper);
        });
    } else {
        // التقارير الأخرى - نسخ الجداول فقط
        const tables = document.querySelectorAll('#reportContent .table');
        tables.forEach(table => {
            const clonedTable = table.cloneNode(true);
            // إزالة أزرار الإجراءات
            const actionButtons = clonedTable.querySelectorAll('.btn-group, .btn');
            actionButtons.forEach(btn => btn.remove());

            const wrapper = document.createElement('div');
            wrapper.className = 'mb-4';
            wrapper.appendChild(clonedTable);
            printableContent.appendChild(wrapper);
        });
    }

    // إضافة معلومات الطباعة
    const printInfo = createPrintInfo();
    printableContent.appendChild(printInfo);
}

/**
 * إنشاء رأس التقرير
 */
function createReportHeader(reportType) {
    const header = document.createElement('div');
    header.className = 'report-header text-center mb-4 page-break-avoid';
    header.style.borderBottom = '2px solid #000';
    header.style.paddingBottom = '15px';
    header.style.marginBottom = '20px';

    const title = getReportTitle(reportType);
    const period = '<?php echo date("d/m/Y", strtotime($start_date)) . " - " . date("d/m/Y", strtotime($end_date)); ?>';

    header.innerHTML = `
        <h1 style="margin: 0 0 10px 0; font-size: 18pt; font-weight: bold;">شركة المبيعات المتقدمة</h1>
        <h2 style="margin: 0 0 10px 0; font-size: 16pt; font-weight: bold;">${title}</h2>
        <p style="margin: 0; font-size: 12pt;">الفترة: ${period}</p>
        <?php if ($customer_id > 0): ?>
        <p style="margin: 5px 0 0 0; font-size: 12pt;">العميل: <?php echo htmlspecialchars($customer_name ?? ''); ?></p>
        <?php endif; ?>
    `;

    return header;
}

/**
 * إنشاء معلومات الطباعة
 */
function createPrintInfo() {
    const printInfo = document.createElement('div');
    printInfo.className = 'print-info';
    printInfo.style.cssText = `
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        text-align: center;
        font-size: 8pt;
        padding: 5px;
        border-top: 1px solid #000;
        background: white;
    `;

    const now = new Date();
    const dateStr = now.toLocaleDateString('ar-SA');
    const timeStr = now.toLocaleTimeString('ar-SA');

    printInfo.innerHTML = `
        <p style="margin: 0;">
            تاريخ الطباعة: ${dateStr} ${timeStr} |
            المستخدم: <?php echo $_SESSION['username'] ?? 'غير محدد'; ?> |
            نوع التقرير: ${getReportTitle('<?php echo $report_type; ?>')}
        </p>
    `;

    return printInfo;
}

// طباعة كشف الحساب خاص
<?php if ($report_type == 'account_statement'): ?>
document.getElementById('printAccountStatement').addEventListener('click', function() {
    preparePrintContent();
    printAccountStatement();
});
<?php endif; ?>

/**
 * طباعة كشف الحساب المحسنة
 */
function printAccountStatement() {
    const printableContent = document.getElementById('printableContent');

    if (!printableContent || printableContent.innerHTML.trim() === '') {
        preparePrintContent();
    }

    const options = {
        title: 'كشف الحساب الشامل',
        orientation: 'landscape',
        paperSize: 'A4',
        styles: true
    };

    printElement('printableContent', options);
}

// تحسين عرض الطباعة
window.addEventListener('beforeprint', function() {
    // إظهار معلومات الطباعة
    const printInfo = document.querySelector('.print-info');
    if (printInfo) {
        printInfo.style.display = 'block';
    }

    // تحسين عرض الجداول
    const tables = document.querySelectorAll('.table');
    tables.forEach(table => {
        table.style.fontSize = '8px';
        table.style.lineHeight = '1.1';
    });
});

window.addEventListener('afterprint', function() {
    // إخفاء معلومات الطباعة
    const printInfo = document.querySelector('.print-info');
    if (printInfo) {
        printInfo.style.display = 'none';
    }

    // إعادة تعيين أحجام الجداول
    const tables = document.querySelectorAll('.table');
    tables.forEach(table => {
        table.style.fontSize = '';
        table.style.lineHeight = '';
    });
});

// تحسين تجربة الطباعة للجداول الكبيرة
function optimizeTableForPrint() {
    const accountTable = document.querySelector('#reportContent .table');
    if (accountTable && window.matchMedia('print').matches) {
        // تقليل حجم الخط للجداول الكبيرة
        const rows = accountTable.querySelectorAll('tbody tr');
        if (rows.length > 20) {
            accountTable.style.fontSize = '7px';
        }
    }
}

// تطبيق التحسينات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    optimizeTableForPrint();
});
</script>

<?php require_once 'includes/footer.php'; ?>