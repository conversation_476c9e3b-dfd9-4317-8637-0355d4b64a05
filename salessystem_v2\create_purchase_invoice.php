<?php
session_start();
require_once __DIR__.'/config/init.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    echo "يجب تسجيل الدخول أولاً";
    exit;
}

echo "<h2>إنشاء فاتورة مشتريات تجريبية</h2>";

$user_id = $_SESSION['user_id'];

// 1. التحقق من وجود منتجات
$products_result = $db->query("SELECT id, name, price, tax_rate FROM products LIMIT 3");
$products = [];
while ($product = $products_result->fetch_assoc()) {
    $products[] = $product;
}

if (empty($products)) {
    echo "❌ لا توجد منتجات. يجب إضافة منتجات أولاً.<br>";
    echo "<a href='add_sample_data.php'>إضافة بيانات تجريبية</a>";
    exit;
}

echo "✅ تم العثور على " . count($products) . " منتجات<br>";

// 2. التحقق من وجود عملاء/موردين
$customers_result = $db->query("SELECT id, name FROM customers WHERE user_id = $user_id LIMIT 1");
$customer_id = null;
if ($customers_result && $customers_result->num_rows > 0) {
    $customer = $customers_result->fetch_assoc();
    $customer_id = $customer['id'];
    echo "✅ سيتم استخدام العميل: {$customer['name']}<br>";
} else {
    echo "⚠️ لا توجد عملاء، سيتم إنشاء الفاتورة بدون عميل<br>";
}

// 3. إنشاء فاتورة مشتريات
$invoice_number = "PURCH-" . date('Y') . "-" . str_pad(rand(1000, 9999), 4, '0', STR_PAD_LEFT);
$date = date('Y-m-d');

echo "<h3>إنشاء فاتورة: $invoice_number</h3>";

// حساب المجاميع
$subtotal = 0;
$tax_amount = 0;
$items_data = [];

foreach ($products as $product) {
    $quantity = rand(1, 3);
    $unit_price = $product['price'];
    $tax_rate = $product['tax_rate'];
    
    $item_subtotal = $quantity * $unit_price;
    $item_tax = $item_subtotal * ($tax_rate / 100);
    $item_total = $item_subtotal + $item_tax;
    
    $subtotal += $item_subtotal;
    $tax_amount += $item_tax;
    
    $items_data[] = [
        'product_id' => $product['id'],
        'product_name' => $product['name'],
        'quantity' => $quantity,
        'unit_price' => $unit_price,
        'tax_rate' => $tax_rate,
        'tax_amount' => $item_tax,
        'total_price' => $item_total
    ];
    
    echo "- {$product['name']}: {$quantity} × {$unit_price} = {$item_total} ر.س<br>";
}

$total_amount = $subtotal + $tax_amount;

echo "<strong>المجموع الفرعي: " . number_format($subtotal, 2) . " ر.س</strong><br>";
echo "<strong>الضريبة: " . number_format($tax_amount, 2) . " ر.س</strong><br>";
echo "<strong>الإجمالي: " . number_format($total_amount, 2) . " ر.س</strong><br>";

// 4. إدراج فاتورة المشتريات
$stmt = $db->prepare("INSERT INTO purchases (user_id, invoice_number, date, customer_id, subtotal, tax_amount, total_amount, payment_status, paid_amount, remaining_amount, notes) VALUES (?, ?, ?, ?, ?, ?, ?, 'unpaid', 0, ?, 'فاتورة مشتريات تجريبية')");
$stmt->bind_param("issidddd", $user_id, $invoice_number, $date, $customer_id, $subtotal, $tax_amount, $total_amount, $total_amount);

if ($stmt->execute()) {
    $purchase_id = $db->insert_id;
    echo "<h3>✅ تم إنشاء فاتورة المشتريات بنجاح!</h3>";
    echo "ID الفاتورة: $purchase_id<br>";
    
    // 5. إضافة عناصر الفاتورة
    echo "<h4>إضافة عناصر الفاتورة:</h4>";
    
    foreach ($items_data as $item) {
        $item_stmt = $db->prepare("INSERT INTO purchase_items (user_id, purchase_id, product_id, product_name, quantity, unit_price, tax_rate, tax_amount, total_price) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
        $item_stmt->bind_param("iiisidddd", 
            $user_id, 
            $purchase_id, 
            $item['product_id'], 
            $item['product_name'], 
            $item['quantity'], 
            $item['unit_price'], 
            $item['tax_rate'], 
            $item['tax_amount'], 
            $item['total_price']
        );
        
        if ($item_stmt->execute()) {
            echo "✅ تم إضافة عنصر: {$item['product_name']}<br>";
        } else {
            echo "❌ فشل إضافة عنصر: {$item['product_name']} - " . $item_stmt->error . "<br>";
        }
    }
    
    echo "<hr>";
    echo "<h3>اختبار الفاتورة:</h3>";
    echo "<a href='edit_purchase.php?id=$purchase_id' target='_blank' class='btn btn-primary'>تعديل فاتورة المشتريات</a><br><br>";
    echo "<a href='view_purchase.php?id=$purchase_id' target='_blank' class='btn btn-info'>عرض فاتورة المشتريات</a><br><br>";
    echo "<a href='purchases.php' class='btn btn-secondary'>صفحة المشتريات</a><br><br>";
    
    // إنشاء فاتورة مبيعات أيضاً إذا لم تكن موجودة
    $sales_check = $db->query("SELECT COUNT(*) as count FROM sales WHERE user_id = $user_id");
    $sales_count = $sales_check->fetch_assoc()['count'];
    
    if ($sales_count == 0) {
        echo "<h3>إنشاء فاتورة مبيعات تجريبية أيضاً:</h3>";
        
        $sale_invoice_number = "SALE-" . date('Y') . "-" . str_pad(rand(1000, 9999), 4, '0', STR_PAD_LEFT);
        
        $sale_stmt = $db->prepare("INSERT INTO sales (user_id, invoice_number, date, customer_id, subtotal, tax_amount, total_amount, payment_status, paid_amount, remaining_amount, notes) VALUES (?, ?, ?, ?, ?, ?, ?, 'unpaid', 0, ?, 'فاتورة مبيعات تجريبية')");
        $sale_stmt->bind_param("issidddd", $user_id, $sale_invoice_number, $date, $customer_id, $subtotal, $tax_amount, $total_amount, $total_amount);
        
        if ($sale_stmt->execute()) {
            $sale_id = $db->insert_id;
            echo "✅ تم إنشاء فاتورة مبيعات: $sale_invoice_number (ID: $sale_id)<br>";
            
            // إضافة عناصر فاتورة المبيعات
            foreach ($items_data as $item) {
                $sale_item_stmt = $db->prepare("INSERT INTO sale_items (user_id, sale_id, product_id, product_name, quantity, unit_price, tax_rate, tax_amount, total_price) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
                $sale_item_stmt->bind_param("iiisidddd", 
                    $user_id, 
                    $sale_id, 
                    $item['product_id'], 
                    $item['product_name'], 
                    $item['quantity'], 
                    $item['unit_price'], 
                    $item['tax_rate'], 
                    $item['tax_amount'], 
                    $item['total_price']
                );
                $sale_item_stmt->execute();
            }
            
            echo "<a href='edit_sale.php?id=$sale_id' target='_blank' class='btn btn-success'>تعديل فاتورة المبيعات</a><br><br>";
        }
    }
    
} else {
    echo "❌ فشل إنشاء فاتورة المشتريات: " . $stmt->error . "<br>";
}

echo "<hr>";
echo "<a href='check_data.php'>فحص البيانات</a> | ";
echo "<a href='index.php'>الصفحة الرئيسية</a>";
?>
