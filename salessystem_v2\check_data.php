<?php
session_start();
require_once __DIR__.'/config/init.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    echo "يجب تسجيل الدخول أولاً";
    exit;
}

echo "<h2>فحص البيانات الموجودة</h2>";

$user_id = $_SESSION['user_id'];

// 1. فحص المستخدم الحالي
echo "<h3>1. المستخدم الحالي:</h3>";
echo "User ID: $user_id<br>";

// 2. فحص العملاء
echo "<h3>2. العملاء:</h3>";
$customers_result = $db->query("SELECT id, name, customer_type FROM customers WHERE user_id = $user_id");
if ($customers_result && $customers_result->num_rows > 0) {
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>الاسم</th><th>النوع</th></tr>";
    while ($customer = $customers_result->fetch_assoc()) {
        echo "<tr><td>{$customer['id']}</td><td>{$customer['name']}</td><td>{$customer['customer_type']}</td></tr>";
    }
    echo "</table>";
} else {
    echo "❌ لا توجد عملاء<br>";
}

// 3. فحص المنتجات
echo "<h3>3. المنتجات:</h3>";
$products_result = $db->query("SELECT id, name, price FROM products LIMIT 10");
if ($products_result && $products_result->num_rows > 0) {
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>الاسم</th><th>السعر</th></tr>";
    while ($product = $products_result->fetch_assoc()) {
        echo "<tr><td>{$product['id']}</td><td>{$product['name']}</td><td>{$product['price']}</td></tr>";
    }
    echo "</table>";
} else {
    echo "❌ لا توجد منتجات<br>";
}

// 4. فحص فواتير المبيعات
echo "<h3>4. فواتير المبيعات:</h3>";
$sales_result = $db->query("SELECT id, invoice_number, date, total_amount FROM sales WHERE user_id = $user_id");
if ($sales_result && $sales_result->num_rows > 0) {
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>رقم الفاتورة</th><th>التاريخ</th><th>المبلغ</th><th>إجراءات</th></tr>";
    while ($sale = $sales_result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>{$sale['id']}</td>";
        echo "<td>{$sale['invoice_number']}</td>";
        echo "<td>{$sale['date']}</td>";
        echo "<td>{$sale['total_amount']}</td>";
        echo "<td><a href='edit_sale.php?id={$sale['id']}' target='_blank'>تعديل</a></td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "❌ لا توجد فواتير مبيعات<br>";
}

// 5. فحص فواتير المشتريات
echo "<h3>5. فواتير المشتريات:</h3>";
$purchases_result = $db->query("SELECT id, invoice_number, date, total_amount FROM purchases WHERE user_id = $user_id");
if ($purchases_result && $purchases_result->num_rows > 0) {
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>رقم الفاتورة</th><th>التاريخ</th><th>المبلغ</th><th>إجراءات</th></tr>";
    while ($purchase = $purchases_result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>{$purchase['id']}</td>";
        echo "<td>{$purchase['invoice_number']}</td>";
        echo "<td>{$purchase['date']}</td>";
        echo "<td>{$purchase['total_amount']}</td>";
        echo "<td><a href='edit_purchase.php?id={$purchase['id']}' target='_blank'>تعديل</a></td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "❌ لا توجد فواتير مشتريات<br>";
}

// 6. فحص عناصر المبيعات
echo "<h3>6. عناصر المبيعات:</h3>";
$sale_items_result = $db->query("SELECT COUNT(*) as count FROM sale_items WHERE user_id = $user_id");
$sale_items_count = $sale_items_result->fetch_assoc()['count'];
echo "عدد عناصر المبيعات: $sale_items_count<br>";

// 7. فحص عناصر المشتريات
echo "<h3>7. عناصر المشتريات:</h3>";
$purchase_items_result = $db->query("SELECT COUNT(*) as count FROM purchase_items WHERE user_id = $user_id");
$purchase_items_count = $purchase_items_result->fetch_assoc()['count'];
echo "عدد عناصر المشتريات: $purchase_items_count<br>";

echo "<hr>";
echo "<h3>إجراءات:</h3>";
echo "<a href='add_sample_data.php'>إضافة بيانات تجريبية</a><br>";
echo "<a href='sales.php'>صفحة المبيعات</a><br>";
echo "<a href='purchases.php'>صفحة المشتريات</a><br>";
echo "<a href='products.php'>صفحة المنتجات</a><br>";
echo "<a href='customers.php'>صفحة العملاء</a><br>";
?>
