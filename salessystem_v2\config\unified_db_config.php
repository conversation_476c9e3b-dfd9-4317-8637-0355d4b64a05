<?php
/**
 * إعدادات قاعدة البيانات الموحدة المحسنة
 * جميع الجداول في قاعدة بيانات واحدة مع الاعتماد على user_id للفصل
 * تم استخدام class configuration بدلاً من define للمرونة
 */

/**
 * فئة إعدادات قاعدة البيانات
 */
class DatabaseConfig {
    // إعدادات قاعدة البيانات الافتراضية
    private static $config = [
        'host' => '127.0.0.1',
        'name' => 'u193708811_system_main',
        'user' => 'sales01',
        'pass' => 'dNz35nd5@',
        'port' => 3306,
        'charset' => 'utf8mb4',
        'timezone' => '+03:00',
        'timeout' => 10,
        'retry_attempts' => 3,
        'retry_delay' => 500000 // 0.5 ثانية بالميكروثانية
    ];

    // إعدادات بديلة للاتصال (في حالة فشل الإعدادات الأساسية)
    private static $fallback_config = [
        'host' => 'localhost',
        'name' => 'u193708811_system_main',
        'user' => 'sales01',
        'pass' => 'dNz35nd5@',
        'port' => 3306
    ];

    /**
     * الحصول على قيمة إعداد معين
     */
    public static function get($key, $default = null) {
        return isset(self::$config[$key]) ? self::$config[$key] : $default;
    }

    /**
     * تعيين قيمة إعداد معين
     */
    public static function set($key, $value) {
        self::$config[$key] = $value;
    }

    /**
     * الحصول على جميع الإعدادات
     */
    public static function getAll() {
        return self::$config;
    }

    /**
     * الحصول على الإعدادات البديلة
     */
    public static function getFallback() {
        return self::$fallback_config;
    }

    /**
     * تحديث الإعدادات من ملف خارجي أو array
     */
    public static function updateConfig($new_config) {
        if (is_array($new_config)) {
            self::$config = array_merge(self::$config, $new_config);
            return true;
        }
        return false;
    }

    /**
     * تحميل الإعدادات من ملف JSON
     */
    public static function loadFromJson($file_path) {
        if (!file_exists($file_path)) {
            return false;
        }

        $json_content = file_get_contents($file_path);
        $config_data = json_decode($json_content, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("خطأ في تحليل ملف JSON: " . json_last_error_msg());
            return false;
        }

        if (isset($config_data['database']['primary'])) {
            $primary = $config_data['database']['primary'];
            self::$config = array_merge(self::$config, [
                'host' => $primary['host'],
                'name' => $primary['name'],
                'user' => $primary['user'],
                'pass' => $primary['pass'],
                'port' => $primary['port'],
                'charset' => $primary['charset'] ?? 'utf8mb4',
                'timezone' => $primary['timezone'] ?? '+03:00'
            ]);
        }

        if (isset($config_data['database']['fallback'])) {
            self::$fallback_config = $config_data['database']['fallback'];
        }

        if (isset($config_data['connection'])) {
            $connection = $config_data['connection'];
            self::$config = array_merge(self::$config, [
                'timeout' => $connection['timeout'] ?? 10,
                'retry_attempts' => $connection['retry_attempts'] ?? 3,
                'retry_delay' => $connection['retry_delay'] ?? 500000
            ]);
        }

        return true;
    }

    /**
     * حفظ الإعدادات الحالية إلى ملف JSON
     */
    public static function saveToJson($file_path) {
        $config_data = [
            'database' => [
                'primary' => [
                    'host' => self::$config['host'],
                    'name' => self::$config['name'],
                    'user' => self::$config['user'],
                    'pass' => self::$config['pass'],
                    'port' => self::$config['port'],
                    'charset' => self::$config['charset'],
                    'timezone' => self::$config['timezone']
                ],
                'fallback' => self::$fallback_config
            ],
            'connection' => [
                'timeout' => self::$config['timeout'],
                'retry_attempts' => self::$config['retry_attempts'],
                'retry_delay' => self::$config['retry_delay']
            ],
            'last_updated' => date('Y-m-d H:i:s')
        ];

        $json_content = json_encode($config_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        return file_put_contents($file_path, $json_content) !== false;
    }

    /**
     * إعادة تعيين الإعدادات للقيم الافتراضية
     */
    public static function reset() {
        self::$config = [
            'host' => '127.0.0.1',
            'name' => 'u193708811_system_main',
            'user' => 'sales01',
            'pass' => 'dNz35nd5@',
            'port' => 3306,
            'charset' => 'utf8mb4',
            'timezone' => '+03:00',
            'timeout' => 10,
            'retry_attempts' => 3,
            'retry_delay' => 500000
        ];
    }
}

// متغيرات الاتصال العامة
$unified_db = null;
$main_db = null; // للتوافق مع النظام القديم

// دالة الاتصال بقاعدة البيانات الموحدة المحسنة (تستخدم Class Configuration)
function getUnifiedDB() {
    global $unified_db;

    // إذا كان الاتصال موجود ويعمل، اختبره أولاً
    if ($unified_db && !$unified_db->connect_error) {
        // اختبار سريع للتأكد من أن الاتصال ما زال نشطاً
        if (@$unified_db->ping()) {
            return $unified_db;
        }
    }

    // الحصول على إعدادات الاتصال
    $config = DatabaseConfig::getAll();
    $max_attempts = $config['retry_attempts'];
    $attempt = 0;

    while ($attempt < $max_attempts) {
        try {
            $attempt++;

            // تحديد الإعدادات للمحاولة الحالية
            $current_config = ($attempt === 1) ? $config : DatabaseConfig::getFallback();

            // إنشاء اتصال جديد
            $unified_db = new mysqli(
                $current_config['host'],
                $current_config['user'],
                $current_config['pass'],
                $current_config['name'],
                $current_config['port']
            );

            // تعيين خيارات الاتصال
            $unified_db->options(MYSQLI_OPT_CONNECT_TIMEOUT, $config['timeout']);
            $unified_db->options(MYSQLI_OPT_INT_AND_FLOAT_NATIVE, 1);

            // فحص الاتصال
            if ($unified_db->connect_error) {
                throw new Exception("خطأ في الاتصال (المحاولة $attempt مع " . $current_config['host'] . "): " . $unified_db->connect_error);
            }

            // تعيين ترميز UTF8
            if (!$unified_db->set_charset($config['charset'])) {
                throw new Exception("خطأ في تعيين الترميز: " . $unified_db->error);
            }

            // تعيين المنطقة الزمنية
            $unified_db->query("SET time_zone = '" . $config['timezone'] . "'");

            // تعيين وضع SQL الآمن
            $unified_db->query("SET sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'");

            // نجح الاتصال
            error_log("نجح الاتصال بقاعدة البيانات في المحاولة $attempt مع " . $current_config['host']);
            return $unified_db;

        } catch (Exception $e) {
            error_log("محاولة الاتصال $attempt فشلت: " . $e->getMessage());

            // إذا كانت هذه المحاولة الأخيرة، جرب الإعدادات البديلة
            if ($attempt >= $max_attempts) {
                // محاولة أخيرة مع إعدادات مختلفة
                return tryAlternativeConnection();
            }

            // انتظار قصير قبل المحاولة التالية
            usleep($config['retry_delay']);
        }
    }

    return null;
}

// دالة محاولة الاتصال بطرق بديلة
function tryAlternativeConnection() {
    $alternative_configs = [
        // محاولة مع localhost
        [
            'host' => 'localhost',
            'user' => 'sales01',
            'pass' => 'dNz35nd5@',
            'name' => 'u193708811_system_main',
            'port' => 3306
        ],
        // محاولة مع root (للاختبار)
        [
            'host' => '127.0.0.1',
            'user' => 'root',
            'pass' => '',
            'name' => 'u193708811_system_main',
            'port' => 3306
        ],
        // محاولة مع المستخدم الثاني
        [
            'host' => '127.0.0.1',
            'user' => 'sales02',
            'pass' => 'dNz35nd5@',
            'name' => 'u193708811_system_main',
            'port' => 3306
        ]
    ];

    foreach ($alternative_configs as $index => $alt_config) {
        try {
            error_log("محاولة اتصال بديلة " . ($index + 1) . " مع " . $alt_config['user'] . "@" . $alt_config['host']);

            $db = new mysqli(
                $alt_config['host'],
                $alt_config['user'],
                $alt_config['pass'],
                $alt_config['name'],
                $alt_config['port']
            );

            if (!$db->connect_error) {
                $db->set_charset("utf8mb4");
                error_log("نجح الاتصال البديل مع " . $alt_config['user'] . "@" . $alt_config['host']);

                // تحديث الإعدادات للاستخدام المستقبلي
                DatabaseConfig::updateConfig([
                    'host' => $alt_config['host'],
                    'user' => $alt_config['user'],
                    'pass' => $alt_config['pass']
                ]);

                return $db;
            }
        } catch (Exception $e) {
            error_log("فشل الاتصال البديل " . ($index + 1) . ": " . $e->getMessage());
            continue;
        }
    }

    error_log("فشلت جميع محاولات الاتصال البديلة");
    return null;
}

// دوال التوافق مع النظام القديم
function getOperationsDB() {
    return getUnifiedDB();
}

function getMainDB() {
    return getUnifiedDB();
}

// تعيين المتغير العام للتوافق
function initializeGlobalConnections() {
    global $main_db, $unified_db;
    $main_db = getUnifiedDB();
    $unified_db = $main_db;
    return $main_db;
}

// دالة اختبار الاتصال المحسنة
function testDatabaseConnection() {
    $config = DatabaseConfig::getAll();
    $db = getUnifiedDB();

    if (!$db) {
        return [
            'success' => false,
            'error' => 'فشل في إنشاء الاتصال',
            'details' => 'لا يمكن الاتصال بقاعدة البيانات',
            'config_used' => [
                'host' => $config['host'],
                'name' => $config['name'],
                'user' => $config['user'],
                'port' => $config['port']
            ]
        ];
    }

    try {
        // اختبار استعلام بسيط
        $result = $db->query("SELECT 1 as test, NOW() as current_time, @@version as mysql_version");
        if ($result) {
            $row = $result->fetch_assoc();
            return [
                'success' => true,
                'message' => 'نجح الاتصال بقاعدة البيانات',
                'server_info' => $db->server_info,
                'host_info' => $db->host_info,
                'mysql_version' => $row['mysql_version'],
                'current_time' => $row['current_time'],
                'config_used' => [
                    'host' => $config['host'],
                    'name' => $config['name'],
                    'user' => $config['user'],
                    'port' => $config['port'],
                    'charset' => $config['charset'],
                    'timezone' => $config['timezone']
                ],
                'connection_id' => $db->thread_id
            ];
        } else {
            return [
                'success' => false,
                'error' => 'فشل في تنفيذ الاستعلام',
                'details' => $db->error,
                'config_used' => [
                    'host' => $config['host'],
                    'name' => $config['name'],
                    'user' => $config['user']
                ]
            ];
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => 'استثناء في اختبار الاتصال',
            'details' => $e->getMessage(),
            'config_used' => [
                'host' => $config['host'],
                'name' => $config['name'],
                'user' => $config['user']
            ]
        ];
    }
}

// دالة للحصول على معلومات التكوين الحالي
function getDatabaseConfigInfo() {
    $config = DatabaseConfig::getAll();
    return [
        'current_config' => $config,
        'fallback_config' => DatabaseConfig::getFallback(),
        'config_type' => 'Class-based Configuration (بدلاً من define)',
        'features' => [
            'dynamic_config' => 'إمكانية تغيير الإعدادات أثناء التشغيل',
            'fallback_support' => 'دعم الإعدادات البديلة',
            'retry_mechanism' => 'آلية إعادة المحاولة',
            'alternative_connections' => 'محاولات اتصال بديلة',
            'auto_config_update' => 'تحديث الإعدادات تلقائياً عند نجاح اتصال بديل'
        ]
    ];
}

// دالة لإنشاء جميع الجداول في قاعدة البيانات الموحدة
function createUnifiedTables() {
    $db = getUnifiedDB();
    if (!$db) {
        return false;
    }
    
    // جدول المستخدمين
    $users_sql = "CREATE TABLE IF NOT EXISTS `users` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `username` varchar(50) NOT NULL UNIQUE,
        `email` varchar(100) NOT NULL UNIQUE,
        `password` varchar(255) NOT NULL,
        `full_name` varchar(100) DEFAULT NULL,
        `phone` varchar(20) DEFAULT NULL,
        `status` enum('active','inactive','suspended') DEFAULT 'active',
        `last_login` timestamp NULL DEFAULT NULL,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_username` (`username`),
        KEY `idx_email` (`email`),
        KEY `idx_status` (`status`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    // جدول المدراء
    $admins_sql = "CREATE TABLE IF NOT EXISTS `admins` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `username` varchar(50) NOT NULL UNIQUE,
        `email` varchar(100) NOT NULL UNIQUE,
        `password` varchar(255) NOT NULL,
        `full_name` varchar(100) DEFAULT NULL,
        `role` enum('super_admin','admin','moderator') DEFAULT 'admin',
        `permissions` text DEFAULT NULL,
        `status` enum('active','inactive') DEFAULT 'active',
        `last_login` timestamp NULL DEFAULT NULL,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_username` (`username`),
        KEY `idx_email` (`email`),
        KEY `idx_role` (`role`),
        KEY `idx_status` (`status`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    // جدول سجل النشاطات
    $activity_log_sql = "CREATE TABLE IF NOT EXISTS `activity_log` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) DEFAULT NULL,
        `user_type` enum('user','admin') DEFAULT 'user',
        `action` varchar(100) NOT NULL,
        `table_name` varchar(50) DEFAULT NULL,
        `record_id` int(11) DEFAULT NULL,
        `old_data` text DEFAULT NULL,
        `new_data` text DEFAULT NULL,
        `description` text DEFAULT NULL,
        `ip_address` varchar(45) DEFAULT NULL,
        `user_agent` text DEFAULT NULL,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_user_id` (`user_id`),
        KEY `idx_user_type` (`user_type`),
        KEY `idx_action` (`action`),
        KEY `idx_table_name` (`table_name`),
        KEY `idx_created_at` (`created_at`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    // جدول العملاء (بدون بادئة)
    $customers_sql = "CREATE TABLE IF NOT EXISTS `customers` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `name` varchar(255) NOT NULL,
        `phone` varchar(20) DEFAULT NULL,
        `email` varchar(255) DEFAULT NULL,
        `tax_number` varchar(50) DEFAULT NULL,
        `address` text DEFAULT NULL,
        `customer_type` enum('customer','supplier') NOT NULL DEFAULT 'customer',
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_user_id` (`user_id`),
        KEY `idx_name` (`name`),
        KEY `idx_email` (`email`),
        KEY `idx_phone` (`phone`),
        KEY `idx_customer_type` (`customer_type`),
        FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    // جدول المنتجات (بدون بادئة)
    $products_sql = "CREATE TABLE IF NOT EXISTS `products` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `name` varchar(255) NOT NULL,
        `description` text DEFAULT NULL,
        `price` decimal(10,2) NOT NULL DEFAULT 0.00,
        `tax_rate` decimal(5,2) NOT NULL DEFAULT 15.00,
        `category` varchar(100) DEFAULT NULL,
        `stock_quantity` decimal(10,2) DEFAULT 0.00,
        `unit` varchar(50) DEFAULT 'قطعة',
        `barcode` varchar(100) DEFAULT NULL,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_user_id` (`user_id`),
        KEY `idx_name` (`name`),
        KEY `idx_category` (`category`),
        KEY `idx_barcode` (`barcode`),
        FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    // جدول المبيعات (بدون بادئة)
    $sales_sql = "CREATE TABLE IF NOT EXISTS `sales` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `invoice_number` varchar(50) NOT NULL,
        `date` date NOT NULL,
        `customer_id` int(11) DEFAULT NULL,
        `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00,
        `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
        `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
        `payment_status` enum('paid','unpaid','partial') DEFAULT 'unpaid',
        `notes` text DEFAULT NULL,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_user_id` (`user_id`),
        UNIQUE KEY `idx_invoice_number` (`invoice_number`),
        KEY `idx_customer_id` (`customer_id`),
        KEY `idx_date` (`date`),
        KEY `idx_payment_status` (`payment_status`),
        FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
        FOREIGN KEY (`customer_id`) REFERENCES `customers`(`id`) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    // جدول المشتريات (بدون بادئة)
    $purchases_sql = "CREATE TABLE IF NOT EXISTS `purchases` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `invoice_number` varchar(50) NOT NULL,
        `date` date NOT NULL,
        `customer_id` int(11) DEFAULT NULL,
        `supplier_name` varchar(255) DEFAULT NULL,
        `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00,
        `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
        `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
        `payment_status` enum('paid','unpaid','partial') DEFAULT 'unpaid',
        `notes` text DEFAULT NULL,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_user_id` (`user_id`),
        UNIQUE KEY `idx_invoice_number` (`invoice_number`),
        KEY `idx_customer_id` (`customer_id`),
        KEY `idx_date` (`date`),
        KEY `idx_payment_status` (`payment_status`),
        FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
        FOREIGN KEY (`customer_id`) REFERENCES `customers`(`id`) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    // جدول عناصر المبيعات (بدون بادئة)
    $sale_items_sql = "CREATE TABLE IF NOT EXISTS `sale_items` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `sale_id` int(11) NOT NULL,
        `product_id` int(11) NOT NULL,
        `product_name` varchar(255) NOT NULL DEFAULT '',
        `quantity` decimal(10,2) NOT NULL DEFAULT 0.00,
        `unit_price` decimal(10,2) NOT NULL DEFAULT 0.00,
        `price` decimal(10,2) NOT NULL DEFAULT 0.00,
        `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
        `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
        `total_price` decimal(10,2) NOT NULL DEFAULT 0.00,
        PRIMARY KEY (`id`),
        KEY `idx_user_id` (`user_id`),
        KEY `idx_sale_id` (`sale_id`),
        KEY `idx_product_id` (`product_id`),
        FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
        FOREIGN KEY (`sale_id`) REFERENCES `sales`(`id`) ON DELETE CASCADE,
        FOREIGN KEY (`product_id`) REFERENCES `products`(`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    // جدول عناصر المشتريات (بدون بادئة)
    $purchase_items_sql = "CREATE TABLE IF NOT EXISTS `purchase_items` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `purchase_id` int(11) NOT NULL,
        `product_id` int(11) NOT NULL,
        `product_name` varchar(255) NOT NULL DEFAULT '',
        `quantity` decimal(10,2) NOT NULL DEFAULT 0.00,
        `unit_price` decimal(10,2) NOT NULL DEFAULT 0.00,
        `price` decimal(10,2) NOT NULL DEFAULT 0.00,
        `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
        `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
        `total_price` decimal(10,2) NOT NULL DEFAULT 0.00,
        PRIMARY KEY (`id`),
        KEY `idx_user_id` (`user_id`),
        KEY `idx_purchase_id` (`purchase_id`),
        KEY `idx_product_id` (`product_id`),
        FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
        FOREIGN KEY (`purchase_id`) REFERENCES `purchases`(`id`) ON DELETE CASCADE,
        FOREIGN KEY (`product_id`) REFERENCES `products`(`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    // تنفيذ إنشاء الجداول
    $tables = [
        'users' => $users_sql,
        'admins' => $admins_sql,
        'activity_log' => $activity_log_sql,
        'customers' => $customers_sql,
        'products' => $products_sql,
        'sales' => $sales_sql,
        'purchases' => $purchases_sql,
        'sale_items' => $sale_items_sql,
        'purchase_items' => $purchase_items_sql
    ];
    
    foreach ($tables as $table_name => $sql) {
        if (!$db->query($sql)) {
            error_log("خطأ في إنشاء جدول $table_name: " . $db->error);
            return false;
        }
    }
    
    return true;
}

// دالة للتوافق - إرجاع اسم الجدول بدون بادئة
function getUserTableName($table_name, $username = null) {
    // في النظام الموحد، نستخدم أسماء الجداول مباشرة بدون بادئة
    return $table_name;
}

// دالة userTableExists() موجودة في includes/database_helper.php

// دالة لإدراج البيانات مع user_id تلقائياً
function insertWithUserId($table, $data, $username = null) {
    if (!$username && isset($_SESSION['username'])) {
        $username = $_SESSION['username'];
    }
    
    if (!$username || !isset($_SESSION['user_id'])) {
        return false;
    }
    
    // إضافة user_id للبيانات
    $data['user_id'] = $_SESSION['user_id'];
    
    // بناء استعلام الإدراج
    $db = getUnifiedDB();
    if (!$db) {
        return false;
    }
    
    $columns = array_keys($data);
    $placeholders = array_fill(0, count($data), '?');
    $values = array_values($data);
    
    $sql = "INSERT INTO `$table` (`" . implode('`, `', $columns) . "`) VALUES (" . implode(', ', $placeholders) . ")";
    
    $stmt = $db->prepare($sql);
    if (!$stmt) {
        return false;
    }
    
    // تحديد أنواع البيانات
    $types = str_repeat('s', count($values));
    $stmt->bind_param($types, ...$values);
    
    $result = $stmt->execute();
    $insert_id = $result ? $db->insert_id : false;
    $stmt->close();
    
    return $insert_id;
}

// دالة لتحديث البيانات مع فلترة user_id تلقائياً
function updateWithUserId($table, $data, $where, $username = null) {
    if (!isset($_SESSION['user_id'])) {
        return false;
    }
    
    $db = getUnifiedDB();
    if (!$db) {
        return false;
    }
    
    // بناء استعلام التحديث مع فلترة user_id
    $set_clauses = [];
    $values = [];
    foreach ($data as $column => $value) {
        $set_clauses[] = "`$column` = ?";
        $values[] = $value;
    }
    
    $where_clause = $where . " AND `user_id` = ?";
    $values[] = $_SESSION['user_id'];
    
    $sql = "UPDATE `$table` SET " . implode(', ', $set_clauses) . " WHERE $where_clause";
    
    $stmt = $db->prepare($sql);
    if (!$stmt) {
        return false;
    }
    
    $types = str_repeat('s', count($values));
    $stmt->bind_param($types, ...$values);
    
    $result = $stmt->execute();
    $affected_rows = $result ? $stmt->affected_rows : 0;
    $stmt->close();
    
    return $affected_rows;
}

// تحميل الإعدادات من ملف JSON إذا كان موجوداً
$json_config_file = __DIR__ . '/database_config.json';
if (file_exists($json_config_file)) {
    if (DatabaseConfig::loadFromJson($json_config_file)) {
        error_log("تم تحميل إعدادات قاعدة البيانات من ملف JSON بنجاح");
    } else {
        error_log("فشل في تحميل إعدادات قاعدة البيانات من ملف JSON");
    }
}

// تهيئة الاتصالات العامة
initializeGlobalConnections();

// إنشاء الجداول تلقائياً عند تحميل الملف
createUnifiedTables();
?>
