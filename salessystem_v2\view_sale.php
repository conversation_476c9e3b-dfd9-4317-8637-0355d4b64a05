<?php
require_once __DIR__.'/config/init.php';
require_once __DIR__.'/includes/functions.php';  // هذا الملف يحتوي على displayMessages()

// التحقق من وضع النافذة المنبثقة
$is_modal = isset($_GET['modal']) && $_GET['modal'] == '1';

if (!$is_modal) {
    require_once __DIR__.'/includes/header.php';
}
redirectIfNotLoggedIn();

$db = getCurrentUserDB();
if ($db === null || $db->connect_error) {
    $_SESSION['error'] = "فشل الاتصال بقاعدة البيانات: " . ($db ? $db->connect_error : "اتصال غير موجود");
    header("Location: sales.php");
    exit();
}

// تنظيف أي نتائج متبقية
$db = resetDBConnection($db);

if (!isset($_GET['id'])) {
    header("Location: sales.php");
    exit();
}

$sale_id = intval($_GET['id']);
$sale = null;
$items = null;

try {
    // جلب بيانات الفاتورة مع معلومات الدفع
    $stmt = $db->prepare("SELECT s.*, c.name AS customer_name, c.address AS customer_address,
                         c.phone AS customer_phone, c.tax_number AS customer_tax_number
                         FROM sales s
                         LEFT JOIN customers c ON s.customer_id = c.id
                         WHERE s.id = ?");
    if (!$stmt) {
        throw new Exception("خطأ في إعداد استعلام الفاتورة: " . $db->error);
    }

    $stmt->bind_param("i", $sale_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $sale = $result->fetch_assoc();
    $stmt->close(); // إغلاق الاستعلام

    if (!$sale) {
        $_SESSION['error'] = "فاتورة المبيعات غير موجودة";
        header("Location: sales.php");
        exit();
    }

    // تنظيف أي نتائج متبقية
    $db = resetDBConnection($db);

    // جلب عناصر الفاتورة
    $query = "SELECT si.*, p.name AS product_name
              FROM sale_items si
              JOIN products p ON si.product_id = p.id
              WHERE si.sale_id = ?";
    $stmt = $db->prepare($query);
    if (!$stmt) {
        throw new Exception("خطأ في إعداد استعلام عناصر الفاتورة: " . $db->error);
    }

    $stmt->bind_param("i", $sale_id);
    $stmt->execute();
    $items = $stmt->get_result();

} catch (Exception $e) {
    error_log("خطأ في عرض الفاتورة: " . $e->getMessage());
    $_SESSION['error'] = "حدث خطأ أثناء جلب بيانات الفاتورة";
    header("Location: sales.php");
    exit();
}

if (!$is_modal) {
    displayMessages(); // عرض أي رسائل خطأ أو نجاح
}
?>

<?php if (!$is_modal): ?>
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5>عرض فاتورة مبيعات</h5>
                    <div>
                        <a href="print_invoice.php?id=<?php echo $sale_id; ?>&type=sale" class="btn btn-sm btn-secondary" target="_blank">
                            <i class="fas fa-print"></i> طباعة
                        </a>
                        <a href="sales.php" class="btn btn-sm btn-primary">
                            <i class="fas fa-arrow-left"></i> رجوع
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
<?php else: ?>
<!-- محتوى النافذة المنبثقة -->
<div class="modal-content-wrapper">
<?php endif; ?>
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6>معلومات العميل:</h6>
                        <p>
                            <strong>الاسم:</strong> <?php echo htmlspecialchars($sale['customer_name'] ?? 'N/A'); ?><br>
                            <strong>الهاتف:</strong> <?php echo htmlspecialchars($sale['customer_phone'] ?? 'N/A'); ?><br>
                            <strong>الرقم الضريبي:</strong> <?php echo htmlspecialchars($sale['customer_tax_number'] ?? 'N/A'); ?><br>
                            <strong>العنوان:</strong> <?php echo htmlspecialchars($sale['customer_address'] ?? 'N/A'); ?>
                        </p>
                    </div>
                    <div class="col-md-6 text-end">
                        <h6>معلومات الفاتورة:</h6>
                        <p>
                            <strong>رقم الفاتورة:</strong> <?php echo $sale['invoice_number']; ?><br>
                            <strong>التاريخ:</strong> <?php echo $sale['date']; ?><br>
                            <?php
                            // ترجمة حالة الدفع
                            $payment_statuses = [
                                'paid' => ['text' => 'مدفوع بالكامل', 'class' => 'bg-success'],
                                'partial' => ['text' => 'مدفوع جزئياً', 'class' => 'bg-warning'],
                                'unpaid' => ['text' => 'غير مدفوع', 'class' => 'bg-danger']
                            ];
                            $status = $payment_statuses[$sale['payment_status'] ?? 'unpaid'] ?? ['text' => 'غير محدد', 'class' => 'bg-secondary'];
                            ?>
                            <strong>حالة الدفع:</strong> <span class="badge <?php echo $status['class']; ?>"><?php echo $status['text']; ?></span>
                        </p>
                    </div>
                </div>

                <!-- قسم معلومات الدفع -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card bg-light">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-credit-card me-2"></i>
                                    معلومات الدفع
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>طريقة الدفع:</strong><br>
                                        <?php
                                        $payment_methods = [
                                            'cash' => 'نقدي',
                                            'card' => 'بطاقة ائتمان',
                                            'bank_transfer' => 'تحويل بنكي',
                                            'check' => 'شيك',
                                            'installment' => 'تقسيط',
                                            'other' => 'أخرى'
                                        ];
                                        echo $payment_methods[$sale['payment_method'] ?? 'cash'] ?? 'نقدي';
                                        ?>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>المبلغ المدفوع:</strong><br>
                                        <span class="text-success fw-bold"><?php echo number_format($sale['paid_amount'] ?? 0, 2); ?> ر.س</span>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>المبلغ المتبقي:</strong><br>
                                        <?php if (($sale['remaining_amount'] ?? 0) > 0): ?>
                                            <span class="text-danger fw-bold"><?php echo number_format($sale['remaining_amount'], 2); ?> ر.س</span>
                                        <?php else: ?>
                                            <span class="text-success fw-bold">0.00 ر.س</span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>تاريخ الدفع:</strong><br>
                                        <?php echo $sale['payment_date'] ? date('d/m/Y', strtotime($sale['payment_date'])) : 'غير محدد'; ?>
                                    </div>
                                </div>
                                <?php if (!empty($sale['payment_reference'])): ?>
                                <div class="row mt-2">
                                    <div class="col-md-6">
                                        <strong>مرجع الدفع:</strong><br>
                                        <?php echo htmlspecialchars($sale['payment_reference']); ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                                <?php if (!empty($sale['payment_notes'])): ?>
                                <div class="row mt-2">
                                    <div class="col-md-12">
                                        <strong>ملاحظات الدفع:</strong><br>
                                        <?php echo htmlspecialchars($sale['payment_notes']); ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>المنتج</th>
                                <th>الكمية</th>
                                <th>السعر</th>
                                <th>الضريبة %</th>
                                <th>قيمة الضريبة</th>
                                <th>المجموع</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            try {
                                if ($items && $items->num_rows > 0) {
                                    $counter = 1;
                                    while ($item = $items->fetch_assoc()):
                                        $item_subtotal = $item['quantity'] * $item['unit_price'];
                                    ?>
                                    <tr>
                                        <td><?php echo $counter++; ?></td>
                                        <td><?php echo htmlspecialchars($item['product_name']); ?></td>
                                        <td><?php echo $item['quantity']; ?></td>
                                        <td><?php echo number_format($item['unit_price'], 2); ?> ر.س</td>
                                        <td><?php echo number_format($item['tax_rate'], 2); ?>%</td>
                                        <td><?php echo number_format($item['tax_amount'], 2); ?> ر.س</td>
                                        <td><?php echo number_format($item['total_price'], 2); ?> ر.س</td>
                                    </tr>
                                    <?php
                                    endwhile;
                                } else {
                                    echo '<tr><td colspan="7" class="text-center">لا توجد عناصر في هذه الفاتورة</td></tr>';
                                }
                            } catch (Exception $e) {
                                error_log("خطأ في عرض عناصر الفاتورة: " . $e->getMessage());
                                echo '<tr><td colspan="7" class="text-center">حدث خطأ أثناء جلب عناصر الفاتورة</td></tr>';
                            }
                            ?>
                        </tbody>
                        <tfoot>
                            <tr>
                                <th colspan="5"></th>
                                <th>المجموع الفرعي:</th>
                                <td><?php echo number_format($sale['subtotal'], 2); ?> ر.س</td>
                            </tr>
                            <tr>
                                <th colspan="5"></th>
                                <th>الضريبة:</th>
                                <td><?php echo number_format($sale['tax_amount'], 2); ?> ر.س</td>
                            </tr>
                            <tr class="table-primary">
                                <th colspan="5"></th>
                                <th>الإجمالي:</th>
                                <td class="fw-bold"><?php echo number_format($sale['total_amount'], 2); ?> ر.س</td>
                            </tr>
                            <tr class="table-success">
                                <th colspan="5"></th>
                                <th>المبلغ المدفوع:</th>
                                <td class="text-success fw-bold"><?php echo number_format($sale['paid_amount'] ?? 0, 2); ?> ر.س</td>
                            </tr>
                            <tr class="<?php echo (($sale['remaining_amount'] ?? 0) > 0) ? 'table-danger' : 'table-success'; ?>">
                                <th colspan="5"></th>
                                <th>المبلغ المتبقي:</th>
                                <td class="<?php echo (($sale['remaining_amount'] ?? 0) > 0) ? 'text-danger' : 'text-success'; ?> fw-bold">
                                    <?php echo number_format($sale['remaining_amount'] ?? 0, 2); ?> ر.س
                                </td>
                            </tr>
                        </tfoot>
                    </table>
                </div>

                <?php if (!empty($sale['notes'])): ?>
                <div class="mt-3">
                    <h6>ملاحظات:</h6>
                    <p><?php echo htmlspecialchars($sale['notes']); ?></p>
                </div>
                <?php endif; ?>

<?php if (!$is_modal): ?>
            </div>
        </div>
    </div>
</div>
<?php else: ?>
</div>
<?php endif; ?>

<?php
if (!$is_modal) {
    require_once 'includes/footer.php';
}
// إغلاق اتصال قاعدة البيانات
if (isset($db) && $db) {
    $db->close();
}
?>