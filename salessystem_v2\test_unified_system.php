<?php
/**
 * اختبار شامل للنظام الموحد الجديد
 * للتحقق من أن جميع الملفات تستخدم التكوين الموحد
 */

require_once __DIR__ . '/config/init.php';

echo "<h2>اختبار النظام الموحد الجديد</h2>";

// اختبار 1: التحقق من الاتصال
echo "<h3>1. اختبار الاتصال بقاعدة البيانات:</h3>";
echo "<div style='background: #f0f0f0; padding: 10px; margin: 10px 0;'>";

$connection_test = testDatabaseConnection();
if ($connection_test['success']) {
    echo "<div style='color: green;'>✅ " . $connection_test['message'] . "</div>";
    echo "<div>معلومات الخادم: " . $connection_test['server_info'] . "</div>";
    echo "<div>معلومات المضيف: " . $connection_test['host_info'] . "</div>";
} else {
    echo "<div style='color: red;'>❌ " . $connection_test['error'] . "</div>";
    echo "<div>التفاصيل: " . $connection_test['details'] . "</div>";
}
echo "</div>";

// اختبار 2: التحقق من الدوال
echo "<h3>2. اختبار الدوال المطلوبة:</h3>";
echo "<div style='background: #f0f0f0; padding: 10px; margin: 10px 0;'>";

$required_functions = [
    'getDB' => 'دالة الاتصال الرئيسية المختصرة',
    'getUnifiedDB' => 'دالة الاتصال الموحدة (للتوافق)',
    'getMainDB' => 'دالة الاتصال الرئيسية (للتوافق)',
    'getOperationsDB' => 'دالة اتصال العمليات (للتوافق)',
    'testDatabaseConnection' => 'دالة اختبار الاتصال المختصرة',
    'createUnifiedTables' => 'دالة إنشاء الجداول الموحدة'
];

foreach ($required_functions as $function => $description) {
    if (function_exists($function)) {
        echo "<div style='color: green;'>✅ $function - $description</div>";
    } else {
        echo "<div style='color: red;'>❌ $function - $description (مفقودة)</div>";
    }
}
echo "</div>";

// اختبار 3: التحقق من طريقة الاتصال
echo "<h3>3. اختبار طريقة الاتصال المختصرة:</h3>";
echo "<div style='background: #f0f0f0; padding: 10px; margin: 10px 0;'>";

echo "<div style='color: green;'>✅ طريقة الاتصال: دالة مختصرة getDB()</div>";
echo "<div style='color: blue;'>📝 الإعدادات مدمجة في الدالة مباشرة</div>";
echo "<div style='color: blue;'>📝 محاولة تلقائية مع 127.0.0.1 ثم localhost</div>";
echo "<div style='color: blue;'>📝 أقصر كود ممكن للاتصال</div>";

// عرض الكود المستخدم
echo "<div style='background: #f8f9fa; padding: 10px; margin: 10px 0; border-left: 3px solid #007bff;'>";
echo "<strong>الكود المستخدم:</strong><br>";
echo "<code style='font-size: 12px;'>";
echo "\$db = @new mysqli('127.0.0.1', 'sales01', 'dNz35nd5@', 'u193708811_system_main')<br>";
echo "&nbsp;&nbsp;&nbsp;&nbsp;?: @new mysqli('localhost', 'sales01', 'dNz35nd5@', 'u193708811_system_main');";
echo "</code>";
echo "</div>";

echo "</div>";

// اختبار 4: التحقق من الجداول
echo "<h3>4. اختبار الجداول المطلوبة:</h3>";
echo "<div style='background: #f0f0f0; padding: 10px; margin: 10px 0;'>";

$db = getDB();
if ($db) {
    $required_tables = [
        'users' => 'جدول المستخدمين',
        'admins' => 'جدول المديرين',
        'activity_log' => 'جدول سجل النشاطات',
        'customers' => 'جدول العملاء',
        'products' => 'جدول المنتجات',
        'sales' => 'جدول المبيعات',
        'purchases' => 'جدول المشتريات',
        'sale_items' => 'جدول عناصر المبيعات',
        'purchase_items' => 'جدول عناصر المشتريات'
    ];
    
    foreach ($required_tables as $table => $description) {
        $check_table = $db->query("SHOW TABLES LIKE '$table'");
        if ($check_table && $check_table->num_rows > 0) {
            // عدد السجلات
            $count_result = $db->query("SELECT COUNT(*) as count FROM `$table`");
            $count = $count_result ? $count_result->fetch_assoc()['count'] : 0;
            echo "<div style='color: green;'>✅ $table - $description ($count سجل)</div>";
        } else {
            echo "<div style='color: red;'>❌ $table - $description (غير موجود)</div>";
        }
    }
} else {
    echo "<div style='color: red;'>❌ لا يمكن الاتصال بقاعدة البيانات لفحص الجداول</div>";
}
echo "</div>";

// اختبار 5: اختبار العمليات الأساسية
echo "<h3>5. اختبار العمليات الأساسية:</h3>";
echo "<div style='background: #f0f0f0; padding: 10px; margin: 10px 0;'>";

if ($db) {
    try {
        // اختبار استعلام بسيط
        $result = $db->query("SELECT 1 as test");
        if ($result) {
            echo "<div style='color: green;'>✅ الاستعلامات البسيطة تعمل</div>";
        }
        
        // اختبار استعلام معقد
        $result = $db->query("SELECT COUNT(*) as total FROM users");
        if ($result) {
            $row = $result->fetch_assoc();
            echo "<div style='color: green;'>✅ الاستعلامات المعقدة تعمل (إجمالي المستخدمين: " . $row['total'] . ")</div>";
        }
        
        // اختبار الترميز
        $result = $db->query("SELECT 'اختبار الترميز العربي' as test_text");
        if ($result) {
            $row = $result->fetch_assoc();
            echo "<div style='color: green;'>✅ الترميز العربي يعمل: " . $row['test_text'] . "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div style='color: red;'>❌ خطأ في العمليات الأساسية: " . $e->getMessage() . "</div>";
    }
} else {
    echo "<div style='color: red;'>❌ لا يمكن اختبار العمليات بدون اتصال قاعدة البيانات</div>";
}
echo "</div>";

// اختبار 6: التحقق من إزالة الملفات القديمة
echo "<h3>6. التحقق من إزالة الملفات القديمة:</h3>";
echo "<div style='background: #f0f0f0; padding: 10px; margin: 10px 0;'>";

$old_files = [
    'config/db_config.php' => 'ملف التكوين القديم'
];

foreach ($old_files as $file => $description) {
    if (file_exists(__DIR__ . '/' . $file)) {
        echo "<div style='color: orange;'>⚠️ $file - $description (ما زال موجود - يجب حذفه)</div>";
    } else {
        echo "<div style='color: green;'>✅ $file - $description (تم حذفه بنجاح)</div>";
    }
}
echo "</div>";

// ملخص النتائج
echo "<h3>7. ملخص النتائج:</h3>";
echo "<div style='background: #e8f5e8; padding: 15px; margin: 10px 0; border: 1px solid #4caf50;'>";
echo "<h4 style='color: #2e7d32;'>✅ أقصر طريقة اتصال ممكنة!</h4>";
echo "<ul>";
echo "<li><strong>سطر واحد للاتصال:</strong> \$db = getDB();</li>";
echo "<li><strong>الإعدادات مدمجة:</strong> لا حاجة لمتغيرات منفصلة</li>";
echo "<li><strong>محاولة تلقائية:</strong> 127.0.0.1 ثم localhost</li>";
echo "<li><strong>بدون تعقيدات:</strong> لا classes أو arrays معقدة</li>";
echo "<li><strong>دوال التوافق:</strong> تعمل مع النظام القديم</li>";
echo "<li><strong>الحجم:</strong> 24 سطر فقط للملف كاملاً!</li>";
echo "</ul>";
echo "<div style='background: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7;'>";
echo "<strong>الكود الكامل:</strong><br>";
echo "<code style='font-size: 11px;'>";
echo "function getDB() {<br>";
echo "&nbsp;&nbsp;global \$db;<br>";
echo "&nbsp;&nbsp;if (\$db) return \$db;<br>";
echo "&nbsp;&nbsp;\$db = @new mysqli('127.0.0.1', 'sales01', 'dNz35nd5@', 'u193708811_system_main')<br>";
echo "&nbsp;&nbsp;&nbsp;&nbsp;?: @new mysqli('localhost', 'sales01', 'dNz35nd5@', 'u193708811_system_main');<br>";
echo "&nbsp;&nbsp;if (\$db) \$db->set_charset('utf8mb4');<br>";
echo "&nbsp;&nbsp;return \$db;<br>";
echo "}";
echo "</code>";
echo "</div>";
echo "</div>";

echo "<hr>";
echo "<p><strong>ملاحظة:</strong> إذا كانت جميع الاختبارات ناجحة، فإن النظام جاهز للاستخدام مع التكوين الموحد الجديد.</p>";
?>
