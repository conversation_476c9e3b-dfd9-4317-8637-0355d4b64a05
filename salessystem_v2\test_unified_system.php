<?php
/**
 * اختبار شامل للنظام الموحد الجديد
 * للتحقق من أن جميع الملفات تستخدم التكوين الموحد
 */

require_once __DIR__ . '/config/init.php';

echo "<h2>اختبار النظام الموحد الجديد</h2>";

// اختبار 1: التحقق من الاتصال
echo "<h3>1. اختبار الاتصال بقاعدة البيانات:</h3>";
echo "<div style='background: #f0f0f0; padding: 10px; margin: 10px 0;'>";

$connection_test = testDatabaseConnection();
if ($connection_test['success']) {
    echo "<div style='color: green;'>✅ " . $connection_test['message'] . "</div>";
    echo "<div>معلومات الخادم: " . $connection_test['server_info'] . "</div>";
    echo "<div>معلومات المضيف: " . $connection_test['host_info'] . "</div>";
} else {
    echo "<div style='color: red;'>❌ " . $connection_test['error'] . "</div>";
    echo "<div>التفاصيل: " . $connection_test['details'] . "</div>";
}
echo "</div>";

// اختبار 2: التحقق من الدوال
echo "<h3>2. اختبار الدوال المطلوبة:</h3>";
echo "<div style='background: #f0f0f0; padding: 10px; margin: 10px 0;'>";

$required_functions = [
    'getUnifiedDB' => 'دالة الاتصال الموحدة',
    'getMainDB' => 'دالة الاتصال الرئيسية (للتوافق)',
    'getOperationsDB' => 'دالة اتصال العمليات (للتوافق)',
    'testDatabaseConnection' => 'دالة اختبار الاتصال',
    'createUnifiedTables' => 'دالة إنشاء الجداول الموحدة',
    'insertWithUserId' => 'دالة الإدراج مع معرف المستخدم',
    'updateWithUserId' => 'دالة التحديث مع معرف المستخدم'
];

foreach ($required_functions as $function => $description) {
    if (function_exists($function)) {
        echo "<div style='color: green;'>✅ $function - $description</div>";
    } else {
        echo "<div style='color: red;'>❌ $function - $description (مفقودة)</div>";
    }
}
echo "</div>";

// اختبار 3: التحقق من إعدادات قاعدة البيانات
echo "<h3>3. اختبار إعدادات قاعدة البيانات:</h3>";
echo "<div style='background: #f0f0f0; padding: 10px; margin: 10px 0;'>";

global $db_config;
if (isset($db_config) && is_array($db_config)) {
    echo "<div style='color: green;'>✅ متغير إعدادات قاعدة البيانات موجود</div>";
    echo "<div>العنوان: " . $db_config['host'] . "</div>";
    echo "<div>قاعدة البيانات: " . $db_config['name'] . "</div>";
    echo "<div>المستخدم: " . $db_config['user'] . "</div>";
    echo "<div>المنفذ: " . $db_config['port'] . "</div>";
    echo "<div style='color: blue;'>📝 يتم استخدام متغير array بدلاً من define للمرونة</div>";
} else {
    echo "<div style='color: red;'>❌ متغير إعدادات قاعدة البيانات مفقود</div>";
}
echo "</div>";

// اختبار 4: التحقق من الجداول
echo "<h3>4. اختبار الجداول المطلوبة:</h3>";
echo "<div style='background: #f0f0f0; padding: 10px; margin: 10px 0;'>";

$db = getUnifiedDB();
if ($db) {
    $required_tables = [
        'users' => 'جدول المستخدمين',
        'admins' => 'جدول المديرين',
        'activity_log' => 'جدول سجل النشاطات',
        'customers' => 'جدول العملاء',
        'products' => 'جدول المنتجات',
        'sales' => 'جدول المبيعات',
        'purchases' => 'جدول المشتريات',
        'sale_items' => 'جدول عناصر المبيعات',
        'purchase_items' => 'جدول عناصر المشتريات'
    ];
    
    foreach ($required_tables as $table => $description) {
        $check_table = $db->query("SHOW TABLES LIKE '$table'");
        if ($check_table && $check_table->num_rows > 0) {
            // عدد السجلات
            $count_result = $db->query("SELECT COUNT(*) as count FROM `$table`");
            $count = $count_result ? $count_result->fetch_assoc()['count'] : 0;
            echo "<div style='color: green;'>✅ $table - $description ($count سجل)</div>";
        } else {
            echo "<div style='color: red;'>❌ $table - $description (غير موجود)</div>";
        }
    }
} else {
    echo "<div style='color: red;'>❌ لا يمكن الاتصال بقاعدة البيانات لفحص الجداول</div>";
}
echo "</div>";

// اختبار 5: اختبار العمليات الأساسية
echo "<h3>5. اختبار العمليات الأساسية:</h3>";
echo "<div style='background: #f0f0f0; padding: 10px; margin: 10px 0;'>";

if ($db) {
    try {
        // اختبار استعلام بسيط
        $result = $db->query("SELECT 1 as test");
        if ($result) {
            echo "<div style='color: green;'>✅ الاستعلامات البسيطة تعمل</div>";
        }
        
        // اختبار استعلام معقد
        $result = $db->query("SELECT COUNT(*) as total FROM users");
        if ($result) {
            $row = $result->fetch_assoc();
            echo "<div style='color: green;'>✅ الاستعلامات المعقدة تعمل (إجمالي المستخدمين: " . $row['total'] . ")</div>";
        }
        
        // اختبار الترميز
        $result = $db->query("SELECT 'اختبار الترميز العربي' as test_text");
        if ($result) {
            $row = $result->fetch_assoc();
            echo "<div style='color: green;'>✅ الترميز العربي يعمل: " . $row['test_text'] . "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div style='color: red;'>❌ خطأ في العمليات الأساسية: " . $e->getMessage() . "</div>";
    }
} else {
    echo "<div style='color: red;'>❌ لا يمكن اختبار العمليات بدون اتصال قاعدة البيانات</div>";
}
echo "</div>";

// اختبار 6: التحقق من إزالة الملفات القديمة
echo "<h3>6. التحقق من إزالة الملفات القديمة:</h3>";
echo "<div style='background: #f0f0f0; padding: 10px; margin: 10px 0;'>";

$old_files = [
    'config/db_config.php' => 'ملف التكوين القديم'
];

foreach ($old_files as $file => $description) {
    if (file_exists(__DIR__ . '/' . $file)) {
        echo "<div style='color: orange;'>⚠️ $file - $description (ما زال موجود - يجب حذفه)</div>";
    } else {
        echo "<div style='color: green;'>✅ $file - $description (تم حذفه بنجاح)</div>";
    }
}
echo "</div>";

// ملخص النتائج
echo "<h3>7. ملخص النتائج:</h3>";
echo "<div style='background: #e8f5e8; padding: 15px; margin: 10px 0; border: 1px solid #4caf50;'>";
echo "<h4 style='color: #2e7d32;'>✅ تم تبسيط النظام بنجاح!</h4>";
echo "<ul>";
echo "<li>تم حذف ملف التكوين القديم (db_config.php)</li>";
echo "<li>تم استخدام طريقة اتصال بسيطة مع متغير array</li>";
echo "<li>تم إزالة التعقيدات غير الضرورية</li>";
echo "<li>تم الاحتفاظ بمحاولة اتصال بديلة (localhost)</li>";
echo "<li>تم تغيير عنوان الخادم من localhost إلى 127.0.0.1</li>";
echo "<li>جميع الملفات تستخدم الآن unified_db_config.php المبسط</li>";
echo "<li>طريقة الاتصال: متغير array بدلاً من define أو class</li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<p><strong>ملاحظة:</strong> إذا كانت جميع الاختبارات ناجحة، فإن النظام جاهز للاستخدام مع التكوين الموحد الجديد.</p>";
?>
