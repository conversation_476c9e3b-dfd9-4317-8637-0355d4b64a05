<?php
session_start();
require_once __DIR__.'/config/init.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    echo "يجب تسجيل الدخول أولاً";
    exit;
}

echo "<h2>إضافة بيانات تجريبية للاختبار</h2>";

$user_id = $_SESSION['user_id'];

// 1. إضافة عملاء تجريبيين
echo "<h3>1. إضافة عملاء تجريبيين:</h3>";

$sample_customers = [
    ['name' => 'عميل تجريبي 1', 'phone' => '0501234567', 'email' => '<EMAIL>'],
    ['name' => 'عميل تجريبي 2', 'phone' => '0507654321', 'email' => '<EMAIL>'],
    ['name' => 'مورد تجريبي 1', 'phone' => '0509876543', 'email' => '<EMAIL>', 'customer_type' => 'supplier']
];

foreach ($sample_customers as $customer) {
    $type = $customer['customer_type'] ?? 'customer';
    $stmt = $db->prepare("INSERT INTO customers (user_id, name, phone, email, customer_type) VALUES (?, ?, ?, ?, ?)");
    $stmt->bind_param("issss", $user_id, $customer['name'], $customer['phone'], $customer['email'], $type);
    
    if ($stmt->execute()) {
        echo "✅ تم إضافة: {$customer['name']}<br>";
    } else {
        echo "❌ فشل إضافة: {$customer['name']} - " . $stmt->error . "<br>";
    }
}

// 2. إضافة منتجات تجريبية
echo "<h3>2. إضافة منتجات تجريبية:</h3>";

$count_result = $db->query("SELECT COUNT(*) as count FROM products");
$product_count = $count_result->fetch_assoc()['count'];

if ($product_count == 0) {
    $sample_products = [
        ['name' => 'منتج تجريبي 1', 'price' => 100.00, 'tax_rate' => 15.00, 'description' => 'منتج تجريبي للاختبار'],
        ['name' => 'منتج تجريبي 2', 'price' => 200.00, 'tax_rate' => 15.00, 'description' => 'منتج تجريبي آخر'],
        ['name' => 'منتج تجريبي 3', 'price' => 50.00, 'tax_rate' => 15.00, 'description' => 'منتج تجريبي ثالث'],
        ['name' => 'خدمة تجريبية', 'price' => 300.00, 'tax_rate' => 15.00, 'description' => 'خدمة تجريبية'],
        ['name' => 'قطعة غيار', 'price' => 75.00, 'tax_rate' => 15.00, 'description' => 'قطعة غيار تجريبية']
    ];

    foreach ($sample_products as $product) {
        $stmt = $db->prepare("INSERT INTO products (name, description, price, tax_rate, created_by) VALUES (?, ?, ?, ?, ?)");
        $stmt->bind_param("ssddi", 
            $product['name'], 
            $product['description'], 
            $product['price'], 
            $product['tax_rate'], 
            $user_id
        );
        
        if ($stmt->execute()) {
            echo "✅ تم إضافة منتج: {$product['name']}<br>";
        } else {
            echo "❌ فشل إضافة منتج: {$product['name']} - " . $stmt->error . "<br>";
        }
    }
} else {
    echo "✅ المنتجات موجودة بالفعل ($product_count منتج)<br>";
}

// 3. إضافة فواتير مبيعات تجريبية
echo "<h3>3. إضافة فواتير مبيعات تجريبية:</h3>";

// جلب أول عميل
$customer_result = $db->query("SELECT id FROM customers WHERE user_id = $user_id AND customer_type = 'customer' LIMIT 1");
$customer_id = null;
if ($customer_result && $customer_result->num_rows > 0) {
    $customer_id = $customer_result->fetch_assoc()['id'];
}

// جلب المنتجات
$products_result = $db->query("SELECT id, name, price, tax_rate FROM products LIMIT 3");
$products = [];
while ($product = $products_result->fetch_assoc()) {
    $products[] = $product;
}

if (!empty($products)) {
    for ($i = 1; $i <= 3; $i++) {
        $invoice_number = "SALE-" . date('Y') . "-" . str_pad($i, 4, '0', STR_PAD_LEFT);
        $date = date('Y-m-d', strtotime("-$i days"));
        
        // حساب المجاميع
        $subtotal = 0;
        $tax_amount = 0;
        foreach ($products as $product) {
            $quantity = rand(1, 5);
            $item_total = $quantity * $product['price'];
            $item_tax = $item_total * ($product['tax_rate'] / 100);
            $subtotal += $item_total;
            $tax_amount += $item_tax;
        }
        $total_amount = $subtotal + $tax_amount;
        
        // إدراج فاتورة المبيعات
        $stmt = $db->prepare("INSERT INTO sales (user_id, invoice_number, date, customer_id, subtotal, tax_amount, total_amount, payment_status, paid_amount, remaining_amount) VALUES (?, ?, ?, ?, ?, ?, ?, 'unpaid', 0, ?)");
        $stmt->bind_param("issidddd", $user_id, $invoice_number, $date, $customer_id, $subtotal, $tax_amount, $total_amount, $total_amount);
        
        if ($stmt->execute()) {
            $sale_id = $db->insert_id;
            echo "✅ تم إضافة فاتورة مبيعات: $invoice_number (ID: $sale_id)<br>";
            
            // إضافة عناصر الفاتورة
            foreach ($products as $product) {
                $quantity = rand(1, 3);
                $unit_price = $product['price'];
                $tax_rate = $product['tax_rate'];
                $item_total = $quantity * $unit_price;
                $item_tax = $item_total * ($tax_rate / 100);
                $total_price = $item_total + $item_tax;
                
                $item_stmt = $db->prepare("INSERT INTO sale_items (user_id, sale_id, product_id, product_name, quantity, unit_price, tax_rate, tax_amount, total_price) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
                $item_stmt->bind_param("iiisidddd", $user_id, $sale_id, $product['id'], $product['name'], $quantity, $unit_price, $tax_rate, $item_tax, $total_price);
                $item_stmt->execute();
            }
        } else {
            echo "❌ فشل إضافة فاتورة مبيعات: $invoice_number - " . $stmt->error . "<br>";
        }
    }
} else {
    echo "❌ لا توجد منتجات لإنشاء فواتير<br>";
}

// 4. إضافة فواتير مشتريات تجريبية
echo "<h3>4. إضافة فواتير مشتريات تجريبية:</h3>";

// جلب أول مورد
$supplier_result = $db->query("SELECT id FROM customers WHERE user_id = $user_id AND customer_type = 'supplier' LIMIT 1");
$supplier_id = null;
if ($supplier_result && $supplier_result->num_rows > 0) {
    $supplier_id = $supplier_result->fetch_assoc()['id'];
}

if (!empty($products)) {
    for ($i = 1; $i <= 2; $i++) {
        $invoice_number = "PURCH-" . date('Y') . "-" . str_pad($i, 4, '0', STR_PAD_LEFT);
        $date = date('Y-m-d', strtotime("-$i days"));
        
        // حساب المجاميع
        $subtotal = 0;
        $tax_amount = 0;
        foreach ($products as $product) {
            $quantity = rand(1, 3);
            $item_total = $quantity * $product['price'];
            $item_tax = $item_total * ($product['tax_rate'] / 100);
            $subtotal += $item_total;
            $tax_amount += $item_tax;
        }
        $total_amount = $subtotal + $tax_amount;
        
        // إدراج فاتورة المشتريات
        $stmt = $db->prepare("INSERT INTO purchases (user_id, invoice_number, date, customer_id, subtotal, tax_amount, total_amount, payment_status, paid_amount, remaining_amount) VALUES (?, ?, ?, ?, ?, ?, ?, 'unpaid', 0, ?)");
        $stmt->bind_param("issidddd", $user_id, $invoice_number, $date, $supplier_id, $subtotal, $tax_amount, $total_amount, $total_amount);
        
        if ($stmt->execute()) {
            $purchase_id = $db->insert_id;
            echo "✅ تم إضافة فاتورة مشتريات: $invoice_number (ID: $purchase_id)<br>";
            
            // إضافة عناصر الفاتورة
            foreach ($products as $product) {
                $quantity = rand(1, 3);
                $unit_price = $product['price'];
                $tax_rate = $product['tax_rate'];
                $item_total = $quantity * $unit_price;
                $item_tax = $item_total * ($tax_rate / 100);
                $total_price = $item_total + $item_tax;
                
                $item_stmt = $db->prepare("INSERT INTO purchase_items (user_id, purchase_id, product_id, product_name, quantity, unit_price, tax_rate, tax_amount, total_price) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
                $item_stmt->bind_param("iiisidddd", $user_id, $purchase_id, $product['id'], $product['name'], $quantity, $unit_price, $tax_rate, $item_tax, $total_price);
                $item_stmt->execute();
            }
        } else {
            echo "❌ فشل إضافة فاتورة مشتريات: $invoice_number - " . $stmt->error . "<br>";
        }
    }
} else {
    echo "❌ لا توجد منتجات لإنشاء فواتير<br>";
}

echo "<hr>";
echo "<h3>الخلاصة:</h3>";

// عرض الإحصائيات
$customers_count = $db->query("SELECT COUNT(*) as count FROM customers WHERE user_id = $user_id")->fetch_assoc()['count'];
$products_count = $db->query("SELECT COUNT(*) as count FROM products")->fetch_assoc()['count'];
$sales_count = $db->query("SELECT COUNT(*) as count FROM sales WHERE user_id = $user_id")->fetch_assoc()['count'];
$purchases_count = $db->query("SELECT COUNT(*) as count FROM purchases WHERE user_id = $user_id")->fetch_assoc()['count'];

echo "<p>📊 الإحصائيات:</p>";
echo "<ul>";
echo "<li>العملاء: $customers_count</li>";
echo "<li>المنتجات: $products_count</li>";
echo "<li>فواتير المبيعات: $sales_count</li>";
echo "<li>فواتير المشتريات: $purchases_count</li>";
echo "</ul>";

echo "<h3>روابط الاختبار:</h3>";
if ($sales_count > 0) {
    $first_sale = $db->query("SELECT id FROM sales WHERE user_id = $user_id ORDER BY id LIMIT 1")->fetch_assoc();
    echo "<a href='edit_sale.php?id={$first_sale['id']}' target='_blank'>اختبار تعديل مبيعات</a><br>";
}

if ($purchases_count > 0) {
    $first_purchase = $db->query("SELECT id FROM purchases WHERE user_id = $user_id ORDER BY id LIMIT 1")->fetch_assoc();
    echo "<a href='edit_purchase.php?id={$first_purchase['id']}' target='_blank'>اختبار تعديل مشتريات</a><br>";
}

echo "<a href='sales.php'>صفحة المبيعات</a><br>";
echo "<a href='purchases.php'>صفحة المشتريات</a><br>";
echo "<a href='products.php'>صفحة المنتجات</a><br>";
echo "<a href='customers.php'>صفحة العملاء</a><br>";
?>
