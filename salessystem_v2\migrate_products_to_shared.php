<?php
/**
 * ترحيل المنتجات من النظام القديم إلى النظام المشترك الجديد
 */

require_once __DIR__ . '/config/init.php';

echo "<h2>ترحيل المنتجات إلى النظام المشترك</h2>";

$db = getDB();
if (!$db) {
    die("فشل الاتصال بقاعدة البيانات");
}

echo "<div style='background: #f0f0f0; padding: 15px; margin: 10px 0;'>";
echo "<h3>خطوات الترحيل:</h3>";
echo "<ol>";
echo "<li>تحديث هيكل جدول المنتجات</li>";
echo "<li>ترحيل المنتجات الموجودة</li>";
echo "<li>تحديث مراجع المنتجات في الفواتير</li>";
echo "<li>تنظيف البيانات المكررة</li>";
echo "</ol>";
echo "</div>";

// الخطوة 1: تحديث هيكل جدول المنتجات
echo "<h3>1. تحديث هيكل جدول المنتجات:</h3>";
echo "<div style='background: #fff3cd; padding: 10px; margin: 10px 0;'>";

try {
    // إضافة الأعمدة الجديدة إذا لم تكن موجودة
    $columns_to_add = [
        "ADD COLUMN `is_active` tinyint(1) DEFAULT 1",
        "ADD COLUMN `created_by` int(11) DEFAULT NULL",
        "ADD INDEX `idx_active` (`is_active`)",
        "ADD INDEX `idx_created_by` (`created_by`)"
    ];
    
    foreach ($columns_to_add as $column_sql) {
        try {
            $db->query("ALTER TABLE products $column_sql");
            echo "✅ تم إضافة: $column_sql<br>";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate') === false) {
                echo "⚠️ تحذير: $column_sql - " . $e->getMessage() . "<br>";
            } else {
                echo "ℹ️ موجود مسبقاً: $column_sql<br>";
            }
        }
    }
    
    // إزالة عمود user_id إذا كان موجوداً
    try {
        $db->query("ALTER TABLE products DROP COLUMN user_id");
        echo "✅ تم حذف عمود user_id<br>";
    } catch (Exception $e) {
        echo "ℹ️ عمود user_id غير موجود أو تم حذفه مسبقاً<br>";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في تحديث الهيكل: " . $e->getMessage() . "<br>";
}

echo "</div>";

// الخطوة 2: ترحيل المنتجات من جداول المستخدمين
echo "<h3>2. ترحيل المنتجات من جداول المستخدمين:</h3>";
echo "<div style='background: #e8f5e8; padding: 10px; margin: 10px 0;'>";

try {
    // جلب جميع المستخدمين
    $users_result = $db->query("SELECT id, username FROM users ORDER BY id");
    $migrated_products = 0;
    $skipped_products = 0;
    
    if ($users_result && $users_result->num_rows > 0) {
        while ($user = $users_result->fetch_assoc()) {
            $user_id = $user['id'];
            $username = $user['username'];
            
            echo "<h4>ترحيل منتجات المستخدم: $username (ID: $user_id)</h4>";
            
            // البحث عن جدول منتجات المستخدم
            $user_products_table = $username . "_products";
            
            // التحقق من وجود الجدول
            $table_check = $db->query("SHOW TABLES LIKE '$user_products_table'");
            if ($table_check && $table_check->num_rows > 0) {
                // جلب منتجات المستخدم
                $user_products = $db->query("SELECT * FROM `$user_products_table`");
                
                if ($user_products && $user_products->num_rows > 0) {
                    while ($product = $user_products->fetch_assoc()) {
                        // التحقق من عدم وجود المنتج في الجدول المشترك
                        $check_existing = $db->prepare("SELECT id FROM products WHERE name = ?");
                        $check_existing->bind_param("s", $product['name']);
                        $check_existing->execute();
                        $existing = $check_existing->get_result()->fetch_assoc();
                        $check_existing->close();
                        
                        if (!$existing) {
                            // إدراج المنتج في الجدول المشترك
                            $insert_stmt = $db->prepare("INSERT INTO products (name, description, price, tax_rate, category, stock_quantity, unit, barcode, is_active, created_by, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1, ?, ?)");
                            
                            $created_at = $product['created_at'] ?? date('Y-m-d H:i:s');
                            $stock_quantity = $product['stock_quantity'] ?? 0;
                            $unit = $product['unit'] ?? 'قطعة';
                            $barcode = $product['barcode'] ?? null;
                            
                            $insert_stmt->bind_param("ssddsdssiss", 
                                $product['name'],
                                $product['description'],
                                $product['price'],
                                $product['tax_rate'],
                                $product['category'],
                                $stock_quantity,
                                $unit,
                                $barcode,
                                $user_id,
                                $created_at
                            );
                            
                            if ($insert_stmt->execute()) {
                                $new_product_id = $db->insert_id;
                                echo "✅ تم ترحيل: " . $product['name'] . " (ID جديد: $new_product_id)<br>";
                                $migrated_products++;
                                
                                // تحديث مراجع المنتج في فواتير المستخدم
                                updateProductReferences($db, $user_id, $product['id'], $new_product_id);
                                
                            } else {
                                echo "❌ فشل ترحيل: " . $product['name'] . "<br>";
                            }
                            $insert_stmt->close();
                        } else {
                            echo "⚠️ تم تخطي (موجود): " . $product['name'] . "<br>";
                            $skipped_products++;
                            
                            // تحديث المراجع للمنتج الموجود
                            updateProductReferences($db, $user_id, $product['id'], $existing['id']);
                        }
                    }
                } else {
                    echo "ℹ️ لا توجد منتجات للمستخدم $username<br>";
                }
            } else {
                echo "ℹ️ جدول المنتجات غير موجود للمستخدم $username<br>";
            }
        }
    }
    
    echo "<hr>";
    echo "<strong>ملخص الترحيل:</strong><br>";
    echo "✅ المنتجات المرحلة: $migrated_products<br>";
    echo "⚠️ المنتجات المتخطاة: $skipped_products<br>";
    
} catch (Exception $e) {
    echo "❌ خطأ في الترحيل: " . $e->getMessage() . "<br>";
}

echo "</div>";

// الخطوة 3: تنظيف وتحسين
echo "<h3>3. تنظيف وتحسين:</h3>";
echo "<div style='background: #d1ecf1; padding: 10px; margin: 10px 0;'>";

try {
    // تحديث الإحصائيات
    $total_products = $db->query("SELECT COUNT(*) as count FROM products WHERE is_active = 1")->fetch_assoc()['count'];
    echo "✅ إجمالي المنتجات النشطة: $total_products<br>";
    
    $categories_count = $db->query("SELECT COUNT(DISTINCT category) as count FROM products WHERE category IS NOT NULL AND category != ''")->fetch_assoc()['count'];
    echo "✅ عدد التصنيفات: $categories_count<br>";
    
} catch (Exception $e) {
    echo "❌ خطأ في التنظيف: " . $e->getMessage() . "<br>";
}

echo "</div>";

// دالة تحديث مراجع المنتجات في الفواتير
function updateProductReferences($db, $user_id, $old_product_id, $new_product_id) {
    try {
        // تحديث في فواتير المبيعات
        $update_sales = $db->prepare("UPDATE sale_items SET product_id = ? WHERE product_id = ? AND user_id = ?");
        $update_sales->bind_param("iii", $new_product_id, $old_product_id, $user_id);
        $update_sales->execute();
        $sales_updated = $update_sales->affected_rows;
        $update_sales->close();
        
        // تحديث في فواتير المشتريات
        $update_purchases = $db->prepare("UPDATE purchase_items SET product_id = ? WHERE product_id = ? AND user_id = ?");
        $update_purchases->bind_param("iii", $new_product_id, $old_product_id, $user_id);
        $update_purchases->execute();
        $purchases_updated = $update_purchases->affected_rows;
        $update_purchases->close();
        
        if ($sales_updated > 0 || $purchases_updated > 0) {
            echo "&nbsp;&nbsp;📝 تم تحديث المراجع: $sales_updated مبيعات، $purchases_updated مشتريات<br>";
        }
        
    } catch (Exception $e) {
        echo "&nbsp;&nbsp;❌ خطأ في تحديث المراجع: " . $e->getMessage() . "<br>";
    }
}

echo "<div style='background: #d4edda; padding: 15px; margin: 10px 0; border: 1px solid #c3e6cb;'>";
echo "<h3>✅ اكتمل الترحيل!</h3>";
echo "<p>تم ترحيل المنتجات إلى النظام المشترك بنجاح.</p>";
echo "<p><strong>المميزات الجديدة:</strong></p>";
echo "<ul>";
echo "<li>المنتجات مشتركة بين جميع المستخدمين</li>";
echo "<li>تتبع من أضاف كل منتج</li>";
echo "<li>إمكانية تعطيل المنتجات بدلاً من حذفها</li>";
echo "<li>تحسين الأداء وتقليل التكرار</li>";
echo "</ul>";
echo "<p><a href='products.php' class='btn btn-primary'>عرض المنتجات المشتركة</a></p>";
echo "</div>";
?>
