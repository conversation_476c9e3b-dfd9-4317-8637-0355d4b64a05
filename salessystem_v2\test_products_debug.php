<?php
session_start();
require_once __DIR__.'/config/init.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    echo "يجب تسجيل الدخول أولاً";
    exit;
}

echo "<h2>اختبار المنتجات في نماذج التعديل</h2>";

// 1. اختبار الاتصال بقاعدة البيانات
echo "<h3>1. اختبار الاتصال بقاعدة البيانات:</h3>";
if ($db) {
    echo "✅ نجح الاتصال بقاعدة البيانات<br>";
} else {
    echo "❌ فشل الاتصال بقاعدة البيانات<br>";
    exit;
}

// 2. اختبار وجود جدول المنتجات
echo "<h3>2. اختبار وجود جدول المنتجات:</h3>";
$check_table = $db->query("SHOW TABLES LIKE 'products'");
if ($check_table && $check_table->num_rows > 0) {
    echo "✅ جدول المنتجات موجود<br>";
} else {
    echo "❌ جدول المنتجات غير موجود<br>";
    exit;
}

// 3. اختبار عدد المنتجات
echo "<h3>3. اختبار عدد المنتجات:</h3>";
$count_result = $db->query("SELECT COUNT(*) as count FROM products");
if ($count_result) {
    $count = $count_result->fetch_assoc()['count'];
    echo "📊 عدد المنتجات في قاعدة البيانات: $count<br>";
    
    if ($count == 0) {
        echo "⚠️ لا توجد منتجات في قاعدة البيانات<br>";
        echo "<a href='products.php'>انتقل إلى صفحة المنتجات لإضافة منتجات</a><br>";
    }
} else {
    echo "❌ خطأ في استعلام عدد المنتجات: " . $db->error . "<br>";
}

// 4. اختبار جلب المنتجات (نفس الكود المستخدم في edit_sale.php)
echo "<h3>4. اختبار جلب المنتجات:</h3>";
$products_result = $db->query("SELECT id, name, price, tax_rate FROM products ORDER BY name");
if ($products_result) {
    echo "✅ نجح استعلام المنتجات<br>";
    echo "📊 عدد المنتجات المجلبة: " . $products_result->num_rows . "<br>";
    
    $products = [];
    while ($product = $products_result->fetch_assoc()) {
        $products[] = $product;
    }
    
    echo "📋 المنتجات المجلبة:<br>";
    if (empty($products)) {
        echo "⚠️ مصفوفة المنتجات فارغة<br>";
    } else {
        foreach ($products as $product) {
            echo "- ID: {$product['id']}, الاسم: {$product['name']}, السعر: {$product['price']}, الضريبة: {$product['tax_rate']}%<br>";
        }
    }
} else {
    echo "❌ خطأ في استعلام المنتجات: " . $db->error . "<br>";
}

// 5. اختبار JavaScript (محاكاة)
echo "<h3>5. اختبار JavaScript (محاكاة):</h3>";
echo "<script>";
echo "const products = [";
foreach ($products as $product) {
    echo "{id: {$product['id']}, name: '" . addslashes($product['name']) . "', price: {$product['price']}, tax_rate: {$product['tax_rate']}},";
}
echo "];";
echo "console.log('عدد المنتجات في JavaScript:', products.length);";
echo "console.log('المنتجات:', products);";
echo "document.write('📊 عدد المنتجات في JavaScript: ' + products.length + '<br>');";
echo "if (products.length === 0) {";
echo "    document.write('⚠️ مصفوفة المنتجات فارغة في JavaScript<br>');";
echo "} else {";
echo "    document.write('✅ المنتجات متاحة في JavaScript<br>');";
echo "}";
echo "</script>";

// 6. اختبار المبيعات الموجودة
echo "<h3>6. اختبار المبيعات الموجودة:</h3>";
$sales_result = $db->query("SELECT id, invoice_number, date FROM sales WHERE user_id = {$_SESSION['user_id']} ORDER BY id DESC LIMIT 5");
if ($sales_result && $sales_result->num_rows > 0) {
    echo "✅ توجد مبيعات للاختبار:<br>";
    while ($sale = $sales_result->fetch_assoc()) {
        echo "- <a href='edit_sale.php?id={$sale['id']}' target='_blank'>فاتورة #{$sale['id']} - {$sale['invoice_number']} - {$sale['date']}</a><br>";
    }
} else {
    echo "⚠️ لا توجد مبيعات للاختبار<br>";
    echo "<a href='add_sale.php'>إضافة فاتورة مبيعات جديدة</a><br>";
}

// 7. اختبار المشتريات الموجودة
echo "<h3>7. اختبار المشتريات الموجودة:</h3>";
$purchases_result = $db->query("SELECT id, invoice_number, date FROM purchases WHERE user_id = {$_SESSION['user_id']} ORDER BY id DESC LIMIT 5");
if ($purchases_result && $purchases_result->num_rows > 0) {
    echo "✅ توجد مشتريات للاختبار:<br>";
    while ($purchase = $purchases_result->fetch_assoc()) {
        echo "- <a href='edit_purchase.php?id={$purchase['id']}' target='_blank'>فاتورة #{$purchase['id']} - {$purchase['invoice_number']} - {$purchase['date']}</a><br>";
    }
} else {
    echo "⚠️ لا توجد مشتريات للاختبار<br>";
    echo "<a href='add_purchase.php'>إضافة فاتورة مشتريات جديدة</a><br>";
}

echo "<hr>";
echo "<h3>الخلاصة:</h3>";
echo "إذا كانت جميع الاختبارات تظهر ✅، فالمشكلة قد تكون في:<br>";
echo "1. عدم وجود منتجات في قاعدة البيانات<br>";
echo "2. مشكلة في JavaScript<br>";
echo "3. مشكلة في تحميل الصفحة<br>";
echo "<br>";
echo "<a href='products.php'>إدارة المنتجات</a> | ";
echo "<a href='edit_sale.php?id=1'>اختبار تعديل مبيعات</a> | ";
echo "<a href='edit_purchase.php?id=1'>اختبار تعديل مشتريات</a>";
?>
