<?php
/**
 * إضافة حقول الدفع لجداول المبيعات والمشتريات
 */

require_once __DIR__ . '/config/init.php';

echo "<h2>إضافة حقول الدفع لجداول المبيعات والمشتريات</h2>";

$db = getDB();
if (!$db) {
    die("فشل الاتصال بقاعدة البيانات");
}

echo "<div style='background: #f0f0f0; padding: 15px; margin: 10px 0;'>";
echo "<h3>الحقول المطلوب إضافتها:</h3>";
echo "<ul>";
echo "<li><strong>payment_method:</strong> طريقة الدفع (نقدي، بطاقة، تحويل بنكي، شيك، تقسيط، أخرى)</li>";
echo "<li><strong>paid_amount:</strong> المبلغ المدفوع</li>";
echo "<li><strong>remaining_amount:</strong> المبلغ المتبقي</li>";
echo "<li><strong>payment_date:</strong> تاريخ الدفع</li>";
echo "<li><strong>payment_reference:</strong> مرجع الدفع (رقم الشيك، رقم التحويل، إلخ)</li>";
echo "<li><strong>payment_notes:</strong> ملاحظات الدفع</li>";
echo "</ul>";
echo "</div>";

// دالة لفحص وجود عمود
function columnExists($db, $table, $column) {
    $result = $db->query("SHOW COLUMNS FROM `$table` LIKE '$column'");
    return $result && $result->num_rows > 0;
}

// دالة لإضافة عمود
function addColumn($db, $table, $column_sql, $description) {
    try {
        $db->query("ALTER TABLE `$table` $column_sql");
        echo "✅ تم إضافة: $description<br>";
        return true;
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate') !== false) {
            echo "ℹ️ موجود مسبقاً: $description<br>";
            return true;
        } else {
            echo "❌ فشل في إضافة: $description - " . $e->getMessage() . "<br>";
            return false;
        }
    }
}

// تحديث جدول المبيعات
echo "<h3>1. تحديث جدول المبيعات:</h3>";
echo "<div style='background: #e8f5e8; padding: 10px; margin: 10px 0;'>";

$sales_columns = [
    'payment_method' => "ADD COLUMN `payment_method` enum('cash','card','bank_transfer','check','installment','other') DEFAULT 'cash' AFTER `total_amount`",
    'paid_amount' => "ADD COLUMN `paid_amount` decimal(10,2) DEFAULT 0.00 AFTER `payment_status`",
    'remaining_amount' => "ADD COLUMN `remaining_amount` decimal(10,2) DEFAULT 0.00 AFTER `paid_amount`",
    'payment_date' => "ADD COLUMN `payment_date` date DEFAULT NULL AFTER `remaining_amount`",
    'payment_reference' => "ADD COLUMN `payment_reference` varchar(100) DEFAULT NULL AFTER `payment_date`",
    'payment_notes' => "ADD COLUMN `payment_notes` text DEFAULT NULL AFTER `payment_reference`"
];

foreach ($sales_columns as $column => $sql) {
    if (!columnExists($db, 'sales', $column)) {
        addColumn($db, 'sales', $sql, "عمود $column في جدول المبيعات");
    } else {
        echo "ℹ️ موجود مسبقاً: عمود $column في جدول المبيعات<br>";
    }
}

// إضافة فهرس لطريقة الدفع
try {
    $db->query("ALTER TABLE `sales` ADD INDEX `idx_payment_method` (`payment_method`)");
    echo "✅ تم إضافة فهرس payment_method للمبيعات<br>";
} catch (Exception $e) {
    if (strpos($e->getMessage(), 'Duplicate') !== false) {
        echo "ℹ️ فهرس payment_method موجود مسبقاً للمبيعات<br>";
    }
}

echo "</div>";

// تحديث جدول المشتريات
echo "<h3>2. تحديث جدول المشتريات:</h3>";
echo "<div style='background: #fff3cd; padding: 10px; margin: 10px 0;'>";

$purchases_columns = [
    'payment_method' => "ADD COLUMN `payment_method` enum('cash','card','bank_transfer','check','installment','other') DEFAULT 'cash' AFTER `total_amount`",
    'paid_amount' => "ADD COLUMN `paid_amount` decimal(10,2) DEFAULT 0.00 AFTER `payment_status`",
    'remaining_amount' => "ADD COLUMN `remaining_amount` decimal(10,2) DEFAULT 0.00 AFTER `paid_amount`",
    'payment_date' => "ADD COLUMN `payment_date` date DEFAULT NULL AFTER `remaining_amount`",
    'payment_reference' => "ADD COLUMN `payment_reference` varchar(100) DEFAULT NULL AFTER `payment_date`",
    'payment_notes' => "ADD COLUMN `payment_notes` text DEFAULT NULL AFTER `payment_reference`"
];

foreach ($purchases_columns as $column => $sql) {
    if (!columnExists($db, 'purchases', $column)) {
        addColumn($db, 'purchases', $sql, "عمود $column في جدول المشتريات");
    } else {
        echo "ℹ️ موجود مسبقاً: عمود $column في جدول المشتريات<br>";
    }
}

// إضافة فهرس لطريقة الدفع
try {
    $db->query("ALTER TABLE `purchases` ADD INDEX `idx_payment_method` (`payment_method`)");
    echo "✅ تم إضافة فهرس payment_method للمشتريات<br>";
} catch (Exception $e) {
    if (strpos($e->getMessage(), 'Duplicate') !== false) {
        echo "ℹ️ فهرس payment_method موجود مسبقاً للمشتريات<br>";
    }
}

echo "</div>";

// تحديث البيانات الموجودة
echo "<h3>3. تحديث البيانات الموجودة:</h3>";
echo "<div style='background: #d1ecf1; padding: 10px; margin: 10px 0;'>";

try {
    // تحديث المبالغ المدفوعة والمتبقية للمبيعات
    $db->query("UPDATE sales SET 
                paid_amount = CASE 
                    WHEN payment_status = 'paid' THEN total_amount 
                    WHEN payment_status = 'partial' THEN total_amount * 0.5 
                    ELSE 0 
                END,
                remaining_amount = CASE 
                    WHEN payment_status = 'paid' THEN 0 
                    WHEN payment_status = 'partial' THEN total_amount * 0.5 
                    ELSE total_amount 
                END
                WHERE paid_amount IS NULL OR paid_amount = 0");
    
    echo "✅ تم تحديث المبالغ المدفوعة والمتبقية للمبيعات<br>";
    
    // تحديث المبالغ المدفوعة والمتبقية للمشتريات
    $db->query("UPDATE purchases SET 
                paid_amount = CASE 
                    WHEN payment_status = 'paid' THEN total_amount 
                    WHEN payment_status = 'partial' THEN total_amount * 0.5 
                    ELSE 0 
                END,
                remaining_amount = CASE 
                    WHEN payment_status = 'paid' THEN 0 
                    WHEN payment_status = 'partial' THEN total_amount * 0.5 
                    ELSE total_amount 
                END
                WHERE paid_amount IS NULL OR paid_amount = 0");
    
    echo "✅ تم تحديث المبالغ المدفوعة والمتبقية للمشتريات<br>";
    
} catch (Exception $e) {
    echo "❌ خطأ في تحديث البيانات: " . $e->getMessage() . "<br>";
}

echo "</div>";

// التحقق النهائي
echo "<h3>4. التحقق النهائي:</h3>";
echo "<div style='background: #d4edda; padding: 10px; margin: 10px 0;'>";

$required_columns = ['payment_method', 'paid_amount', 'remaining_amount', 'payment_date', 'payment_reference', 'payment_notes'];

echo "<h4>جدول المبيعات:</h4>";
$sales_complete = true;
foreach ($required_columns as $column) {
    $exists = columnExists($db, 'sales', $column);
    echo "- $column: " . ($exists ? "✅ موجود" : "❌ مفقود") . "<br>";
    if (!$exists) $sales_complete = false;
}

echo "<h4>جدول المشتريات:</h4>";
$purchases_complete = true;
foreach ($required_columns as $column) {
    $exists = columnExists($db, 'purchases', $column);
    echo "- $column: " . ($exists ? "✅ موجود" : "❌ مفقود") . "<br>";
    if (!$exists) $purchases_complete = false;
}

if ($sales_complete && $purchases_complete) {
    echo "<br><strong style='color: green;'>✅ تم تحديث جميع الجداول بنجاح!</strong><br>";
    echo "<p>الآن يمكنك استخدام خيارات الدفع في نماذج المبيعات والمشتريات.</p>";
} else {
    echo "<br><strong style='color: red;'>❌ التحديث غير مكتمل!</strong><br>";
    echo "<p>يرجى مراجعة الأخطاء أعلاه وإعادة المحاولة.</p>";
}

// عرض إحصائيات
try {
    $sales_count = $db->query("SELECT COUNT(*) as count FROM sales")->fetch_assoc()['count'];
    $purchases_count = $db->query("SELECT COUNT(*) as count FROM purchases")->fetch_assoc()['count'];
    
    echo "<br><strong>الإحصائيات:</strong><br>";
    echo "- إجمالي فواتير المبيعات: $sales_count<br>";
    echo "- إجمالي فواتير المشتريات: $purchases_count<br>";
    
} catch (Exception $e) {
    echo "❌ خطأ في جلب الإحصائيات: " . $e->getMessage() . "<br>";
}

echo "</div>";

// روابط للخطوات التالية
echo "<div style='background: #e2e3e5; padding: 15px; margin: 10px 0;'>";
echo "<h3>الخطوات التالية:</h3>";
echo "<p><a href='add_sale.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>اختبار إضافة مبيعات</a></p>";
echo "<p><a href='add_purchase.php' style='background: #dc3545; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>اختبار إضافة مشتريات</a></p>";
echo "<p><a href='index.php' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>اختبار الفاتورة السريعة</a></p>";
echo "<p><a href='sales.php' style='background: #17a2b8; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>عرض المبيعات</a></p>";
echo "</div>";
?>
