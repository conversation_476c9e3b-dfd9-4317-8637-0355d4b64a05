<?php
require_once __DIR__.'/config/init.php';
require_once __DIR__.'/includes/functions.php';  // هذا الملف يحتوي على displayMessages()
require_once __DIR__.'/includes/header.php';
redirectIfNotLoggedIn();

$db = getCurrentUserDB();
if ($db === null || $db->connect_error) {
    $_SESSION['error'] = "فشل الاتصال بقاعدة البيانات: " . ($db ? $db->connect_error : "اتصال غير موجود");
    header("Location: purchases.php");
    exit();
}

// تنظيف أي نتائج متبقية
$db = resetDBConnection($db);

if (!isset($_GET['id'])) {
    header("Location: purchases.php");
    exit();
}

$purchase_id = intval($_GET['id']);
$purchase = null;
$items = null;

try {
    // جلب بيانات الفاتورة
    $stmt = $db->prepare("SELECT * FROM purchases WHERE id = ?");
    if (!$stmt) {
        throw new Exception("خطأ في إعداد استعلام الفاتورة: " . $db->error);
    }

    $stmt->bind_param("i", $purchase_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $purchase = $result->fetch_assoc();
    $stmt->close(); // إغلاق الاستعلام

    if (!$purchase) {
        $_SESSION['error'] = "فاتورة المشتريات غير موجودة";
        header("Location: purchases.php");
        exit();
    }

    // تنظيف أي نتائج متبقية
    $db = resetDBConnection($db);

    // جلب عناصر الفاتورة
    $query = "SELECT * FROM purchase_items WHERE purchase_id = ?";
    $stmt = $db->prepare($query);
    if (!$stmt) {
        throw new Exception("خطأ في إعداد استعلام عناصر الفاتورة: " . $db->error);
    }

    $stmt->bind_param("i", $purchase_id);
    $stmt->execute();
    $items = $stmt->get_result();

} catch (Exception $e) {
    error_log("خطأ في عرض الفاتورة: " . $e->getMessage());
    $_SESSION['error'] = "حدث خطأ أثناء جلب بيانات الفاتورة";
    header("Location: purchases.php");
    exit();
}

displayMessages(); // عرض أي رسائل خطأ أو نجاح
?>

<div class="row">
    <div class="col-md-12">
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5>عرض فاتورة مشتريات</h5>
                    <div>
                        <a href="print_invoice.php?id=<?php echo $purchase_id; ?>&type=purchase" class="btn btn-sm btn-secondary" target="_blank">
                            <i class="fas fa-print"></i> طباعة
                        </a>
                        <a href="purchases.php" class="btn btn-sm btn-primary">
                            <i class="fas fa-arrow-left"></i> رجوع
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6>معلومات المورد:</h6>
                        <p>
                            <strong>الاسم:</strong> <?php echo htmlspecialchars($purchase['supplier_name']); ?><br>
                        </p>
                    </div>
                    <div class="col-md-6 text-end">
                        <h6>معلومات الفاتورة:</h6>
                        <p>
                            <strong>رقم الفاتورة:</strong> <?php echo $purchase['invoice_number']; ?><br>
                            <strong>التاريخ:</strong> <?php echo $purchase['date']; ?><br>
                            <?php
                            // ترجمة حالة الدفع
                            $payment_statuses = [
                                'paid' => ['text' => 'مدفوع بالكامل', 'class' => 'bg-success'],
                                'partial' => ['text' => 'مدفوع جزئياً', 'class' => 'bg-warning'],
                                'unpaid' => ['text' => 'غير مدفوع', 'class' => 'bg-danger']
                            ];
                            $status = $payment_statuses[$purchase['payment_status'] ?? 'unpaid'] ?? ['text' => 'غير محدد', 'class' => 'bg-secondary'];
                            ?>
                            <strong>حالة الدفع:</strong> <span class="badge <?php echo $status['class']; ?>"><?php echo $status['text']; ?></span>
                        </p>
                    </div>
                </div>

                <!-- قسم معلومات الدفع -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card bg-light border-danger">
                            <div class="card-header bg-danger text-white">
                                <h6 class="mb-0">
                                    <i class="fas fa-credit-card me-2"></i>
                                    معلومات الدفع
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>طريقة الدفع:</strong><br>
                                        <?php
                                        $payment_methods = [
                                            'cash' => 'نقدي',
                                            'card' => 'بطاقة ائتمان',
                                            'bank_transfer' => 'تحويل بنكي',
                                            'check' => 'شيك',
                                            'installment' => 'تقسيط',
                                            'other' => 'أخرى'
                                        ];
                                        echo $payment_methods[$purchase['payment_method'] ?? 'cash'] ?? 'نقدي';
                                        ?>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>المبلغ المدفوع:</strong><br>
                                        <span class="text-success fw-bold"><?php echo number_format($purchase['paid_amount'] ?? 0, 2); ?> ر.س</span>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>المبلغ المتبقي:</strong><br>
                                        <?php if (($purchase['remaining_amount'] ?? 0) > 0): ?>
                                            <span class="text-danger fw-bold"><?php echo number_format($purchase['remaining_amount'], 2); ?> ر.س</span>
                                        <?php else: ?>
                                            <span class="text-success fw-bold">0.00 ر.س</span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>تاريخ الدفع:</strong><br>
                                        <?php echo $purchase['payment_date'] ? date('d/m/Y', strtotime($purchase['payment_date'])) : 'غير محدد'; ?>
                                    </div>
                                </div>
                                <?php if (!empty($purchase['payment_reference'])): ?>
                                <div class="row mt-2">
                                    <div class="col-md-6">
                                        <strong>مرجع الدفع:</strong><br>
                                        <?php echo htmlspecialchars($purchase['payment_reference']); ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                                <?php if (!empty($purchase['payment_notes'])): ?>
                                <div class="row mt-2">
                                    <div class="col-md-12">
                                        <strong>ملاحظات الدفع:</strong><br>
                                        <?php echo htmlspecialchars($purchase['payment_notes']); ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>المنتج</th>
                                <th>الكمية</th>
                                <th>السعر</th>
                                <th>الضريبة %</th>
                                <th>قيمة الضريبة</th>
                                <th>المجموع</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            try {
                                if ($items && $items->num_rows > 0) {
                                    $counter = 1;
                                    while ($item = $items->fetch_assoc()):
                                        $item_subtotal = $item['quantity'] * $item['unit_price'];
                                    ?>
                                    <tr>
                                        <td><?php echo $counter++; ?></td>
                                        <td><?php echo htmlspecialchars($item['product_name']); ?></td>
                                        <td><?php echo $item['quantity']; ?></td>
                                        <td><?php echo number_format($item['unit_price'], 2); ?> ر.س</td>
                                        <td><?php echo number_format($item['tax_rate'], 2); ?>%</td>
                                        <td><?php echo number_format($item['tax_amount'], 2); ?> ر.س</td>
                                        <td><?php echo number_format($item['total_price'], 2); ?> ر.س</td>
                                    </tr>
                                    <?php
                                    endwhile;
                                } else {
                                    echo '<tr><td colspan="7" class="text-center">لا توجد عناصر في هذه الفاتورة</td></tr>';
                                }
                            } catch (Exception $e) {
                                error_log("خطأ في عرض عناصر الفاتورة: " . $e->getMessage());
                                echo '<tr><td colspan="7" class="text-center">حدث خطأ أثناء جلب عناصر الفاتورة</td></tr>';
                            }
                            ?>
                        </tbody>
                        <tfoot>
                            <tr>
                                <th colspan="5"></th>
                                <th>المجموع الفرعي:</th>
                                <td><?php echo number_format($purchase['subtotal'], 2); ?> ر.س</td>
                            </tr>
                            <tr>
                                <th colspan="5"></th>
                                <th>الضريبة:</th>
                                <td><?php echo number_format($purchase['tax_amount'], 2); ?> ر.س</td>
                            </tr>
                            <tr class="table-primary">
                                <th colspan="5"></th>
                                <th>الإجمالي:</th>
                                <td class="fw-bold"><?php echo number_format($purchase['total_amount'], 2); ?> ر.س</td>
                            </tr>
                            <tr class="table-success">
                                <th colspan="5"></th>
                                <th>المبلغ المدفوع:</th>
                                <td class="text-success fw-bold"><?php echo number_format($purchase['paid_amount'] ?? 0, 2); ?> ر.س</td>
                            </tr>
                            <tr class="<?php echo (($purchase['remaining_amount'] ?? 0) > 0) ? 'table-danger' : 'table-success'; ?>">
                                <th colspan="5"></th>
                                <th>المبلغ المتبقي:</th>
                                <td class="<?php echo (($purchase['remaining_amount'] ?? 0) > 0) ? 'text-danger' : 'text-success'; ?> fw-bold">
                                    <?php echo number_format($purchase['remaining_amount'] ?? 0, 2); ?> ر.س
                                </td>
                            </tr>
                        </tfoot>
                    </table>
                </div>

                <?php if (!empty($purchase['notes'])): ?>
                <div class="mt-3">
                    <h6>ملاحظات:</h6>
                    <p><?php echo htmlspecialchars($purchase['notes']); ?></p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php
require_once 'includes/footer.php';
// إغلاق اتصال قاعدة البيانات
if (isset($db) && $db) {
    $db->close();
}
?>