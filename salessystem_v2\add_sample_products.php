<?php
session_start();
require_once __DIR__.'/config/init.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    echo "يجب تسجيل الدخول أولاً";
    exit;
}

echo "<h2>إضافة منتجات تجريبية</h2>";

// التحقق من وجود منتجات
$count_result = $db->query("SELECT COUNT(*) as count FROM products");
$count = $count_result->fetch_assoc()['count'];

echo "<p>عدد المنتجات الحالية: $count</p>";

if ($count > 0) {
    echo "<p>✅ توجد منتجات بالفعل في قاعدة البيانات</p>";
    echo "<a href='products.php'>عرض المنتجات</a> | ";
    echo "<a href='edit_sale.php?id=1'>اختبار تعديل مبيعات</a>";
    exit;
}

// إضافة منتجات تجريبية
$sample_products = [
    ['name' => 'منتج تجريبي 1', 'price' => 100.00, 'tax_rate' => 15.00, 'description' => 'منتج تجريبي للاختبار'],
    ['name' => 'منتج تجريبي 2', 'price' => 200.00, 'tax_rate' => 15.00, 'description' => 'منتج تجريبي آخر'],
    ['name' => 'منتج تجريبي 3', 'price' => 50.00, 'tax_rate' => 15.00, 'description' => 'منتج تجريبي ثالث'],
    ['name' => 'خدمة تجريبية', 'price' => 300.00, 'tax_rate' => 15.00, 'description' => 'خدمة تجريبية'],
    ['name' => 'قطعة غيار', 'price' => 75.00, 'tax_rate' => 15.00, 'description' => 'قطعة غيار تجريبية']
];

echo "<h3>إضافة المنتجات التجريبية:</h3>";

foreach ($sample_products as $product) {
    $stmt = $db->prepare("INSERT INTO products (name, description, price, tax_rate, created_by) VALUES (?, ?, ?, ?, ?)");
    $stmt->bind_param("ssddi", 
        $product['name'], 
        $product['description'], 
        $product['price'], 
        $product['tax_rate'], 
        $_SESSION['user_id']
    );
    
    if ($stmt->execute()) {
        echo "✅ تم إضافة: {$product['name']}<br>";
    } else {
        echo "❌ فشل إضافة: {$product['name']} - " . $stmt->error . "<br>";
    }
}

// التحقق من النتيجة
$count_result = $db->query("SELECT COUNT(*) as count FROM products");
$new_count = $count_result->fetch_assoc()['count'];

echo "<hr>";
echo "<h3>النتيجة:</h3>";
echo "<p>عدد المنتجات بعد الإضافة: $new_count</p>";

if ($new_count > 0) {
    echo "<p>✅ تم إضافة المنتجات بنجاح!</p>";
    echo "<a href='products.php'>عرض المنتجات</a> | ";
    echo "<a href='edit_sale.php?id=1'>اختبار تعديل مبيعات</a> | ";
    echo "<a href='edit_purchase.php?id=1'>اختبار تعديل مشتريات</a>";
} else {
    echo "<p>❌ فشل في إضافة المنتجات</p>";
}
?>
