<?php
/**
 * اختبار النظام المحدث - المنتجات المشتركة
 */

require_once __DIR__ . '/config/init.php';

echo "<h2>اختبار النظام المحدث - المنتجات المشتركة</h2>";

$db = getDB();
if (!$db) {
    die("فشل الاتصال بقاعدة البيانات");
}

echo "<div style='background: #f0f0f0; padding: 15px; margin: 10px 0;'>";
echo "<h3>اختبارات النظام الجديد:</h3>";

// اختبار 1: هيكل جدول المنتجات
echo "<h4>1. اختبار هيكل جدول المنتجات:</h4>";
try {
    $columns = $db->query("DESCRIBE products");
    $expected_columns = ['id', 'name', 'description', 'price', 'tax_rate', 'category', 'stock_quantity', 'unit', 'barcode', 'is_active', 'created_by', 'created_at', 'updated_at'];
    $found_columns = [];
    
    if ($columns) {
        while ($col = $columns->fetch_assoc()) {
            $found_columns[] = $col['Field'];
        }
    }
    
    $missing_columns = array_diff($expected_columns, $found_columns);
    $extra_columns = array_diff($found_columns, $expected_columns);
    
    if (empty($missing_columns) && in_array('user_id', $extra_columns)) {
        echo "<span style='color: orange;'>⚠️ تحذير: عمود user_id ما زال موجود - يجب حذفه</span><br>";
    } elseif (empty($missing_columns)) {
        echo "<span style='color: green;'>✅ هيكل الجدول صحيح</span><br>";
    } else {
        echo "<span style='color: red;'>❌ أعمدة مفقودة: " . implode(', ', $missing_columns) . "</span><br>";
    }
    
    echo "<strong>الأعمدة الموجودة:</strong> " . implode(', ', $found_columns) . "<br>";
    
} catch (Exception $e) {
    echo "<span style='color: red;'>❌ خطأ في فحص الهيكل: " . $e->getMessage() . "</span><br>";
}

// اختبار 2: الدوال الجديدة
echo "<h4>2. اختبار الدوال الجديدة:</h4>";
$functions_to_test = [
    'isSharedTable' => 'التحقق من الجداول المشتركة',
    'insertProduct' => 'إدراج منتج مشترك',
    'updateProduct' => 'تحديث منتج مشترك'
];

foreach ($functions_to_test as $function => $description) {
    if (function_exists($function)) {
        echo "<span style='color: green;'>✅ $function - $description</span><br>";
    } else {
        echo "<span style='color: red;'>❌ $function - $description (مفقودة)</span><br>";
    }
}

// اختبار 3: المنتجات المشتركة
echo "<h4>3. اختبار المنتجات المشتركة:</h4>";
try {
    $products_count = $db->query("SELECT COUNT(*) as count FROM products WHERE is_active = 1")->fetch_assoc()['count'];
    echo "<span style='color: blue;'>📦 عدد المنتجات النشطة: $products_count</span><br>";
    
    $categories_count = $db->query("SELECT COUNT(DISTINCT category) as count FROM products WHERE category IS NOT NULL AND category != ''")->fetch_assoc()['count'];
    echo "<span style='color: blue;'>🏷️ عدد التصنيفات: $categories_count</span><br>";
    
    $with_creator = $db->query("SELECT COUNT(*) as count FROM products WHERE created_by IS NOT NULL")->fetch_assoc()['count'];
    echo "<span style='color: blue;'>👤 منتجات لها منشئ: $with_creator</span><br>";
    
} catch (Exception $e) {
    echo "<span style='color: red;'>❌ خطأ في فحص المنتجات: " . $e->getMessage() . "</span><br>";
}

// اختبار 4: اختبار الدوال العملية
echo "<h4>4. اختبار الدوال العملية:</h4>";
try {
    // اختبار isSharedTable
    if (function_exists('isSharedTable')) {
        $is_products_shared = isSharedTable('products');
        $is_customers_shared = isSharedTable('customers');
        
        if ($is_products_shared && !$is_customers_shared) {
            echo "<span style='color: green;'>✅ isSharedTable تعمل بشكل صحيح</span><br>";
        } else {
            echo "<span style='color: red;'>❌ isSharedTable لا تعمل بشكل صحيح</span><br>";
        }
    }
    
    // اختبار إدراج منتج تجريبي
    if (function_exists('insertProduct')) {
        $test_product = [
            'name' => 'منتج تجريبي - ' . date('Y-m-d H:i:s'),
            'description' => 'منتج للاختبار',
            'price' => 100.00,
            'tax_rate' => 15.00,
            'category' => 'اختبار'
        ];
        
        $new_id = insertProduct($test_product);
        if ($new_id) {
            echo "<span style='color: green;'>✅ تم إدراج منتج تجريبي (ID: $new_id)</span><br>";
            
            // حذف المنتج التجريبي
            $db->query("DELETE FROM products WHERE id = $new_id");
            echo "<span style='color: blue;'>🗑️ تم حذف المنتج التجريبي</span><br>";
        } else {
            echo "<span style='color: red;'>❌ فشل إدراج المنتج التجريبي</span><br>";
        }
    }
    
} catch (Exception $e) {
    echo "<span style='color: red;'>❌ خطأ في اختبار الدوال: " . $e->getMessage() . "</span><br>";
}

// اختبار 5: اختبار الاستعلامات في الملفات
echo "<h4>5. اختبار الملفات المحدثة:</h4>";
$files_to_check = [
    'products.php' => 'صفحة المنتجات',
    'save_product.php' => 'حفظ المنتج',
    'get_product.php' => 'جلب المنتج',
    'add_sale.php' => 'إضافة مبيعات',
    'add_purchase.php' => 'إضافة مشتريات'
];

foreach ($files_to_check as $file => $description) {
    if (file_exists(__DIR__ . '/' . $file)) {
        $content = file_get_contents(__DIR__ . '/' . $file);
        
        // فحص إزالة فلترة user_id من المنتجات
        $has_old_products_query = preg_match('/products.*WHERE.*user_id/i', $content);
        $has_new_products_query = preg_match('/products.*WHERE.*is_active/i', $content) || 
                                 preg_match('/FROM products.*ORDER BY/i', $content);
        
        if (!$has_old_products_query && $has_new_products_query) {
            echo "<span style='color: green;'>✅ $file - $description (محدث)</span><br>";
        } elseif ($has_old_products_query) {
            echo "<span style='color: orange;'>⚠️ $file - $description (يحتاج تحديث)</span><br>";
        } else {
            echo "<span style='color: blue;'>ℹ️ $file - $description (غير محدد)</span><br>";
        }
    } else {
        echo "<span style='color: red;'>❌ $file - غير موجود</span><br>";
    }
}

echo "</div>";

// اختبار 6: عرض عينة من المنتجات
echo "<h3>6. عينة من المنتجات المشتركة:</h3>";
echo "<div style='background: #e8f5e8; padding: 10px; margin: 10px 0;'>";

try {
    $sample_products = $db->query("SELECT p.*, u.username as created_by_username 
                                   FROM products p 
                                   LEFT JOIN users u ON p.created_by = u.id 
                                   WHERE p.is_active = 1 
                                   ORDER BY p.created_at DESC 
                                   LIMIT 5");
    
    if ($sample_products && $sample_products->num_rows > 0) {
        echo "<table style='width: 100%; border-collapse: collapse;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>ID</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>اسم المنتج</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>السعر</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>التصنيف</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>أضيف بواسطة</th>";
        echo "</tr>";
        
        while ($product = $sample_products->fetch_assoc()) {
            echo "<tr>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . $product['id'] . "</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . htmlspecialchars($product['name']) . "</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . number_format($product['price'], 2) . " ر.س</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . htmlspecialchars($product['category'] ?? 'غير محدد') . "</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . htmlspecialchars($product['created_by_username'] ?? 'غير محدد') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>لا توجد منتجات للعرض</p>";
    }
    
} catch (Exception $e) {
    echo "<span style='color: red;'>❌ خطأ في عرض المنتجات: " . $e->getMessage() . "</span>";
}

echo "</div>";

// ملخص النتائج
echo "<div style='background: #d4edda; padding: 15px; margin: 10px 0; border: 1px solid #c3e6cb;'>";
echo "<h3>✅ ملخص الاختبار:</h3>";
echo "<p><strong>النظام الجديد جاهز!</strong></p>";
echo "<ul>";
echo "<li>✅ المنتجات أصبحت مشتركة بين جميع المستخدمين</li>";
echo "<li>✅ العملاء والفواتير ما زالت خاصة بكل مستخدم</li>";
echo "<li>✅ تتبع من أضاف كل منتج</li>";
echo "<li>✅ إمكانية تعطيل المنتجات</li>";
echo "</ul>";

echo "<h4>الخطوات التالية:</h4>";
echo "<p><a href='migrate_products_to_shared.php' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>تشغيل الترحيل</a></p>";
echo "<p><a href='products.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>عرض المنتجات</a></p>";
echo "<p><a href='index.php' style='background: #17a2b8; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>العودة للرئيسية</a></p>";
echo "</div>";
?>
