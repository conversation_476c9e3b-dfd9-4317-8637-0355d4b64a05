<?php
/**
 * تحديث هيكل جدول المنتجات للنظام المشترك
 */

require_once __DIR__ . '/config/init.php';

echo "<h2>تحديث هيكل جدول المنتجات</h2>";

$db = getDB();
if (!$db) {
    die("فشل الاتصال بقاعدة البيانات");
}

echo "<div style='background: #f0f0f0; padding: 15px; margin: 10px 0;'>";
echo "<h3>خطوات التحديث:</h3>";
echo "<ol>";
echo "<li>فحص الهيكل الحالي</li>";
echo "<li>إضافة الأعمدة المفقودة</li>";
echo "<li>إزالة عمود user_id (إذا كان موجود)</li>";
echo "<li>إضافة الفهارس المطلوبة</li>";
echo "</ol>";
echo "</div>";

// الخطوة 1: فحص الهيكل الحالي
echo "<h3>1. فحص الهيكل الحالي:</h3>";
echo "<div style='background: #e8f5e8; padding: 10px; margin: 10px 0;'>";

try {
    $columns = $db->query("DESCRIBE products");
    $current_columns = [];
    
    if ($columns) {
        echo "<strong>الأعمدة الموجودة:</strong><br>";
        while ($col = $columns->fetch_assoc()) {
            $current_columns[] = $col['Field'];
            echo "- " . $col['Field'] . " (" . $col['Type'] . ")<br>";
        }
    }
    
    echo "<br><strong>تحليل الهيكل:</strong><br>";
    $has_user_id = in_array('user_id', $current_columns);
    $has_is_active = in_array('is_active', $current_columns);
    $has_created_by = in_array('created_by', $current_columns);
    
    echo "- user_id: " . ($has_user_id ? "موجود ❌ (يجب حذفه)" : "غير موجود ✅") . "<br>";
    echo "- is_active: " . ($has_is_active ? "موجود ✅" : "غير موجود ❌ (يجب إضافته)") . "<br>";
    echo "- created_by: " . ($has_created_by ? "موجود ✅" : "غير موجود ❌ (يجب إضافته)") . "<br>";
    
} catch (Exception $e) {
    echo "❌ خطأ في فحص الهيكل: " . $e->getMessage() . "<br>";
}

echo "</div>";

// الخطوة 2: إضافة الأعمدة المفقودة
echo "<h3>2. إضافة الأعمدة المفقودة:</h3>";
echo "<div style='background: #fff3cd; padding: 10px; margin: 10px 0;'>";

$columns_to_add = [];

if (!$has_is_active) {
    $columns_to_add[] = "ADD COLUMN `is_active` tinyint(1) DEFAULT 1 AFTER `barcode`";
}

if (!$has_created_by) {
    $columns_to_add[] = "ADD COLUMN `created_by` int(11) DEFAULT NULL AFTER `is_active`";
}

if (!empty($columns_to_add)) {
    foreach ($columns_to_add as $column_sql) {
        try {
            $db->query("ALTER TABLE products $column_sql");
            echo "✅ تم إضافة: " . str_replace('ADD COLUMN ', '', $column_sql) . "<br>";
        } catch (Exception $e) {
            echo "❌ فشل في إضافة: " . str_replace('ADD COLUMN ', '', $column_sql) . " - " . $e->getMessage() . "<br>";
        }
    }
} else {
    echo "ℹ️ جميع الأعمدة المطلوبة موجودة<br>";
}

echo "</div>";

// الخطوة 3: إزالة عمود user_id
echo "<h3>3. إزالة عمود user_id:</h3>";
echo "<div style='background: #f8d7da; padding: 10px; margin: 10px 0;'>";

if ($has_user_id) {
    try {
        // أولاً، نسخ البيانات إلى created_by إذا كان فارغ
        $db->query("UPDATE products SET created_by = user_id WHERE created_by IS NULL AND user_id IS NOT NULL");
        echo "✅ تم نسخ بيانات user_id إلى created_by<br>";
        
        // ثم حذف عمود user_id
        $db->query("ALTER TABLE products DROP COLUMN user_id");
        echo "✅ تم حذف عمود user_id<br>";
        
    } catch (Exception $e) {
        echo "❌ فشل في حذف عمود user_id: " . $e->getMessage() . "<br>";
    }
} else {
    echo "ℹ️ عمود user_id غير موجود (جيد)<br>";
}

echo "</div>";

// الخطوة 4: إضافة الفهارس
echo "<h3>4. إضافة الفهارس المطلوبة:</h3>";
echo "<div style='background: #d1ecf1; padding: 10px; margin: 10px 0;'>";

$indexes_to_add = [
    "ADD INDEX `idx_active` (`is_active`)",
    "ADD INDEX `idx_created_by` (`created_by`)"
];

foreach ($indexes_to_add as $index_sql) {
    try {
        $db->query("ALTER TABLE products $index_sql");
        echo "✅ تم إضافة: " . str_replace('ADD INDEX ', '', $index_sql) . "<br>";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate') !== false) {
            echo "ℹ️ موجود مسبقاً: " . str_replace('ADD INDEX ', '', $index_sql) . "<br>";
        } else {
            echo "❌ فشل في إضافة: " . str_replace('ADD INDEX ', '', $index_sql) . " - " . $e->getMessage() . "<br>";
        }
    }
}

echo "</div>";

// الخطوة 5: التحقق النهائي
echo "<h3>5. التحقق النهائي:</h3>";
echo "<div style='background: #d4edda; padding: 10px; margin: 10px 0;'>";

try {
    $final_columns = $db->query("DESCRIBE products");
    $final_column_list = [];
    
    if ($final_columns) {
        while ($col = $final_columns->fetch_assoc()) {
            $final_column_list[] = $col['Field'];
        }
    }
    
    $final_has_user_id = in_array('user_id', $final_column_list);
    $final_has_is_active = in_array('is_active', $final_column_list);
    $final_has_created_by = in_array('created_by', $final_column_list);
    
    echo "<strong>الهيكل النهائي:</strong><br>";
    echo "- user_id: " . ($final_has_user_id ? "موجود ❌" : "غير موجود ✅") . "<br>";
    echo "- is_active: " . ($final_has_is_active ? "موجود ✅" : "غير موجود ❌") . "<br>";
    echo "- created_by: " . ($final_has_created_by ? "موجود ✅" : "غير موجود ❌") . "<br>";
    
    if (!$final_has_user_id && $final_has_is_active && $final_has_created_by) {
        echo "<br><strong style='color: green;'>✅ تم تحديث الهيكل بنجاح!</strong><br>";
        echo "<p>الآن يمكنك استخدام النظام الجديد مع المنتجات المشتركة.</p>";
    } else {
        echo "<br><strong style='color: red;'>❌ الهيكل غير مكتمل!</strong><br>";
        echo "<p>يرجى مراجعة الأخطاء أعلاه وإعادة المحاولة.</p>";
    }
    
    // عرض إحصائيات
    $total_products = $db->query("SELECT COUNT(*) as count FROM products")->fetch_assoc()['count'];
    echo "<br><strong>الإحصائيات:</strong><br>";
    echo "- إجمالي المنتجات: $total_products<br>";
    
    if ($final_has_is_active) {
        $active_products = $db->query("SELECT COUNT(*) as count FROM products WHERE is_active = 1")->fetch_assoc()['count'];
        echo "- المنتجات النشطة: $active_products<br>";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في التحقق النهائي: " . $e->getMessage() . "<br>";
}

echo "</div>";

// روابط للخطوات التالية
echo "<div style='background: #e2e3e5; padding: 15px; margin: 10px 0;'>";
echo "<h3>الخطوات التالية:</h3>";
echo "<p><a href='products.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>عرض المنتجات</a></p>";
echo "<p><a href='test_shared_products.php' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>اختبار النظام</a></p>";
echo "<p><a href='index.php' style='background: #17a2b8; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>العودة للرئيسية</a></p>";
echo "</div>";
?>
