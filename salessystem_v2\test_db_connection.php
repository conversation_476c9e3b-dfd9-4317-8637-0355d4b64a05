<?php
/**
 * اختبار الاتصال بقاعدة البيانات - تشخيص شامل
 */

echo "<h2>تشخيص شامل لاتصال قاعدة البيانات</h2>";

// اختبار 1: الاتصال المباشر بدون ملفات التكوين
echo "<h3>1. اختبار الاتصال المباشر:</h3>";
echo "<div style='background: #f0f0f0; padding: 10px; margin: 10px 0;'>";

$host = 'localhost';
$user = 'sales01';
$pass = 'dNz35nd5@';
$dbname = 'u193708811_system_main';
$port = 3306;

echo "<strong>محاولة الاتصال بالمعاملات:</strong><br>";
echo "Host: $host<br>";
echo "User: $user<br>";
echo "Database: $dbname<br>";
echo "Port: $port<br><br>";

try {
    $direct_connection = new mysqli($host, $user, $pass, $dbname, $port);

    if ($direct_connection->connect_error) {
        echo "<div style='color: red;'>❌ فشل الاتصال المباشر!</div>";
        echo "<div style='color: red;'>خطأ: " . $direct_connection->connect_error . "</div>";
        echo "<div style='color: red;'>رقم الخطأ: " . $direct_connection->connect_errno . "</div>";
    } else {
        echo "<div style='color: green;'>✅ نجح الاتصال المباشر!</div>";
        echo "<div style='color: green;'>معلومات الخادم: " . $direct_connection->server_info . "</div>";
        $direct_connection->close();
    }
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ استثناء في الاتصال المباشر: " . $e->getMessage() . "</div>";
}
echo "</div>";

// اختبار 2: اختبار مع 127.0.0.1 بدلاً من localhost
echo "<h3>2. اختبار الاتصال مع 127.0.0.1:</h3>";
echo "<div style='background: #f0f0f0; padding: 10px; margin: 10px 0;'>";

$host_ip = '127.0.0.1';
echo "<strong>محاولة الاتصال مع IP:</strong> $host_ip<br><br>";

try {
    $ip_connection = new mysqli($host_ip, $user, $pass, $dbname, $port);

    if ($ip_connection->connect_error) {
        echo "<div style='color: red;'>❌ فشل الاتصال مع IP!</div>";
        echo "<div style='color: red;'>خطأ: " . $ip_connection->connect_error . "</div>";
        echo "<div style='color: red;'>رقم الخطأ: " . $ip_connection->connect_errno . "</div>";
    } else {
        echo "<div style='color: green;'>✅ نجح الاتصال مع IP!</div>";
        echo "<div style='color: green;'>معلومات الخادم: " . $ip_connection->server_info . "</div>";
        $ip_connection->close();
    }
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ استثناء في الاتصال مع IP: " . $e->getMessage() . "</div>";
}
echo "</div>";

// اختبار 3: اختبار مع المستخدم الثاني
echo "<h3>3. اختبار الاتصال مع المستخدم الثاني (sales02):</h3>";
echo "<div style='background: #f0f0f0; padding: 10px; margin: 10px 0;'>";

$user2 = 'sales02';
$dbname2 = 'u193708811_operations';
echo "<strong>محاولة الاتصال مع:</strong><br>";
echo "User: $user2<br>";
echo "Database: $dbname2<br><br>";

try {
    $user2_connection = new mysqli($host, $user2, $pass, $dbname2, $port);

    if ($user2_connection->connect_error) {
        echo "<div style='color: red;'>❌ فشل الاتصال مع المستخدم الثاني!</div>";
        echo "<div style='color: red;'>خطأ: " . $user2_connection->connect_error . "</div>";
        echo "<div style='color: red;'>رقم الخطأ: " . $user2_connection->connect_errno . "</div>";
    } else {
        echo "<div style='color: green;'>✅ نجح الاتصال مع المستخدم الثاني!</div>";
        echo "<div style='color: green;'>معلومات الخادم: " . $user2_connection->server_info . "</div>";
        $user2_connection->close();
    }
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ استثناء في الاتصال مع المستخدم الثاني: " . $e->getMessage() . "</div>";
}
echo "</div>";

// اختبار 4: اختبار مع root (للمقارنة)
echo "<h3>4. اختبار الاتصال مع root (للمقارنة):</h3>";
echo "<div style='background: #f0f0f0; padding: 10px; margin: 10px 0;'>";

$root_user = 'root';
$root_pass = '';
echo "<strong>محاولة الاتصال مع root (بدون كلمة مرور):</strong><br><br>";

try {
    $root_connection = new mysqli($host, $root_user, $root_pass, '', $port);

    if ($root_connection->connect_error) {
        echo "<div style='color: red;'>❌ فشل الاتصال مع root!</div>";
        echo "<div style='color: red;'>خطأ: " . $root_connection->connect_error . "</div>";
    } else {
        echo "<div style='color: green;'>✅ نجح الاتصال مع root!</div>";
        echo "<div style='color: green;'>معلومات الخادم: " . $root_connection->server_info . "</div>";

        // اختبار وجود قواعد البيانات
        echo "<br><strong>قواعد البيانات الموجودة:</strong><br>";
        $result = $root_connection->query("SHOW DATABASES");
        if ($result) {
            while ($row = $result->fetch_assoc()) {
                $db_name = $row['Database'];
                if (strpos($db_name, 'u193708811') !== false) {
                    echo "<div style='color: blue;'>📁 $db_name</div>";
                }
            }
        }

        // اختبار وجود المستخدمين
        echo "<br><strong>المستخدمين الموجودين:</strong><br>";
        $result = $root_connection->query("SELECT User, Host FROM mysql.user WHERE User LIKE 'sales%'");
        if ($result) {
            while ($row = $result->fetch_assoc()) {
                echo "<div style='color: blue;'>👤 " . $row['User'] . "@" . $row['Host'] . "</div>";
            }
        }

        $root_connection->close();
    }
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ استثناء في الاتصال مع root: " . $e->getMessage() . "</div>";
}
echo "</div>";

// اختبار 5: اختبار ملف التكوين الموحد
echo "<h3>5. اختبار ملف التكوين الموحد:</h3>";
echo "<div style='background: #f0f0f0; padding: 10px; margin: 10px 0;'>";

require_once __DIR__ . '/config/unified_db_config.php';

echo "<strong>إعدادات ملف التكوين الموحد:</strong><br>";
echo "DB_HOST: " . (defined('DB_HOST') ? DB_HOST : 'غير محدد') . "<br>";
echo "DB_NAME: " . (defined('DB_NAME') ? DB_NAME : 'غير محدد') . "<br>";
echo "DB_USER: " . (defined('DB_USER') ? DB_USER : 'غير محدد') . "<br>";
echo "DB_PORT: " . (defined('DB_PORT') ? DB_PORT : 'غير محدد') . "<br><br>";

$unified_db = getUnifiedDB();

if ($unified_db) {
    echo "<div style='color: green;'>✅ نجح الاتصال عبر ملف التكوين الموحد!</div>";

    // اختبار استعلام بسيط
    $result = $unified_db->query("SELECT 1 as test");
    if ($result) {
        echo "<div style='color: green;'>✅ نجح تنفيذ الاستعلام!</div>";
    } else {
        echo "<div style='color: red;'>❌ فشل في تنفيذ الاستعلام: " . $unified_db->error . "</div>";
    }
    
    // اختبار وجود الجداول
    echo "<h3>3. اختبار وجود الجداول:</h3>";
    $tables = ['users', 'customers', 'products', 'sales', 'purchases'];
    foreach ($tables as $table) {
        $result = $db->query("SHOW TABLES LIKE '$table'");
        if ($result && $result->num_rows > 0) {
            echo "<div style='color: green;'>✅ جدول $table موجود</div>";
        } else {
            echo "<div style='color: orange;'>⚠️ جدول $table غير موجود</div>";
        }
    }
    
    // اختبار إنشاء الجداول
    echo "<h3>4. اختبار إنشاء الجداول:</h3>";
    if (createUnifiedTables()) {
        echo "<div style='color: green;'>✅ تم إنشاء/التحقق من الجداول بنجاح!</div>";
    } else {
        echo "<div style='color: red;'>❌ فشل في إنشاء الجداول</div>";
    }
    
    // اختبار جدول المستخدمين
    echo "<h3>5. اختبار جدول المستخدمين:</h3>";
    $result = $db->query("SELECT COUNT(*) as count FROM users");
    if ($result) {
        $row = $result->fetch_assoc();
        echo "<div style='color: green;'>✅ عدد المستخدمين: " . $row['count'] . "</div>";
    } else {
        echo "<div style='color: red;'>❌ فشل في قراءة جدول المستخدمين: " . $db->error . "</div>";
    }
} else {
    echo "<div style='color: red;'>❌ فشل الاتصال عبر ملف التكوين الموحد!</div>";
}
echo "</div>";

// اختبار 6: معلومات النظام
echo "<h3>6. معلومات النظام:</h3>";
echo "<div style='background: #f0f0f0; padding: 10px; margin: 10px 0;'>";
echo "<strong>معلومات PHP و MySQL:</strong><br>";
echo "إصدار PHP: " . phpversion() . "<br>";
echo "إصدار MySQL Client: " . mysqli_get_client_info() . "<br>";
echo "امتداد MySQLi: " . (extension_loaded('mysqli') ? 'مُحمّل' : 'غير مُحمّل') . "<br>";
echo "امتداد PDO: " . (extension_loaded('pdo') ? 'مُحمّل' : 'غير مُحمّل') . "<br>";
echo "امتداد PDO MySQL: " . (extension_loaded('pdo_mysql') ? 'مُحمّل' : 'غير مُحمّل') . "<br>";
echo "</div>";

// توصيات الحل
echo "<h3>7. توصيات الحل:</h3>";
echo "<div style='background: #fff3cd; padding: 15px; margin: 10px 0; border: 1px solid #ffeaa7;'>";
echo "<strong>إذا فشلت جميع الاختبارات أعلاه، جرب الحلول التالية:</strong><br><br>";

echo "<strong>1. تحقق من إعدادات MySQL:</strong><br>";
echo "- تأكد من تشغيل خدمة MySQL<br>";
echo "- تحقق من أن المنفذ 3306 مفتوح<br>";
echo "- تأكد من أن MySQL يقبل الاتصالات من localhost<br><br>";

echo "<strong>2. تحقق من المستخدمين وكلمات المرور:</strong><br>";
echo "- تأكد من وجود المستخدم 'sales01'<br>";
echo "- تحقق من صحة كلمة المرور<br>";
echo "- تأكد من صلاحيات المستخدم<br><br>";

echo "<strong>3. تحقق من قواعد البيانات:</strong><br>";
echo "- تأكد من وجود قاعدة البيانات 'u193708811_system_main'<br>";
echo "- تحقق من صلاحيات الوصول لقاعدة البيانات<br><br>";

echo "<strong>4. أوامر MySQL للتحقق (تشغيل في phpMyAdmin أو MySQL CLI):</strong><br>";
echo "<code style='background: #f8f9fa; padding: 5px; display: block; margin: 5px 0;'>";
echo "SHOW DATABASES;<br>";
echo "SELECT User, Host FROM mysql.user WHERE User LIKE 'sales%';<br>";
echo "SHOW GRANTS FOR 'sales01'@'localhost';<br>";
echo "SHOW GRANTS FOR 'sales01'@'127.0.0.1';<br>";
echo "</code>";

echo "</div>";

echo "<hr>";
echo "<p><strong>ملاحظة:</strong> هذا الاختبار يساعد في تشخيص مشاكل الاتصال بقاعدة البيانات. ";
echo "إذا استمرت المشكلة، تحقق من إعدادات MySQL وصلاحيات المستخدمين.</p>";
?>
    
    // اختبار إعدادات PHP
    echo "<h3>إعدادات PHP:</h3>";
    echo "<ul>";
    echo "<li>إصدار PHP: " . phpversion() . "</li>";
    echo "<li>MySQLi مفعل: " . (extension_loaded('mysqli') ? 'نعم' : 'لا') . "</li>";
    echo "<li>PDO مفعل: " . (extension_loaded('pdo') ? 'نعم' : 'لا') . "</li>";
    echo "</ul>";
}

echo "<hr>";
echo "<h3>معلومات إضافية:</h3>";
echo "<p><strong>الوقت الحالي:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<p><strong>مسار الملف:</strong> " . __FILE__ . "</p>";
?>
