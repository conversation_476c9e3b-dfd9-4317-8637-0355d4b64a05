{"database": {"primary": {"host": "127.0.0.1", "name": "u193708811_system_main", "user": "sales01", "pass": "dNz35nd5@", "port": 3306, "charset": "utf8mb4", "timezone": "+03:00"}, "fallback": {"host": "localhost", "name": "u193708811_system_main", "user": "sales01", "pass": "dNz35nd5@", "port": 3306}, "alternatives": [{"host": "127.0.0.1", "user": "root", "pass": "", "name": "u193708811_system_main", "port": 3306, "description": "Root user connection"}, {"host": "127.0.0.1", "user": "sales02", "pass": "dNz35nd5@", "name": "u193708811_system_main", "port": 3306, "description": "Secondary user connection"}]}, "connection": {"timeout": 10, "retry_attempts": 3, "retry_delay": 500000, "ping_check": true, "auto_reconnect": true}, "sql_settings": {"sql_mode": "STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO", "autocommit": true, "foreign_key_checks": true}, "logging": {"log_connections": true, "log_errors": true, "log_queries": false}}